import Register_Login from "./components/Register_Login";
import {BrowserRouter, Routes,Route} from "react-router-dom"
import Dashboard from "./pages/User/Dashboard";
import { CookiesProvider } from 'react-cookie';
import Abouts from "./pages/User/Abouts";
import Safety1 from "./pages/User/Safety1";
import RenterDashboard1 from "./pages/Renter/RenterDashboard1";
import RoutePages from "./RoutePages";
function App() {
  // const userId = localStorage.getItem('userId');
  return (
    <>
      <CookiesProvider>
        {/* <Dashboard />  */}
        <Routes>
          <Route path="/" element={<RoutePages />} />
          <Route path="/user/dashboard" element={<Dashboard />} />
          <Route path="/renter/dashboard" element={<RenterDashboard1 />} />
          <Route path="/login" element={<Register_Login />} />
          <Route path="/about" element={<Abouts />} />
          <Route path="/safety" element={<Safety1 />} />
        </Routes>
        </CookiesProvider>
    </>
  );
}

export default App;