import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const RoutePages = () => {
  const navigate = useNavigate();
  const UserType = localStorage.getItem('UserType');

  useEffect(() => {
    if (UserType === 'renter') {
      navigate('/renter/dashboard');
    } else if (UserType === 'user') {
      navigate('/user/dashboard');
    }else{
       navigate('/user/dashboard');
    }
  }, [UserType, navigate]);

  return (
    <div>
      {/* Content or loading indicator can go here */}
    </div>
  );
}

export default RoutePages;
