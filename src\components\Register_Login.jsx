import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaEyeSlash } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

const carImages = {
  login: 'https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
  register: 'https://images.unsplash.com/photo-1493238792000-8113da705763?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80'
};

const Register_Login = () => {
  const [username, setUsername] = useState('');
  const [isLogin, setIsLogin] = useState(true);
  const [userType, setUserType] = useState('user');
  const [formData, setFormData] = useState({
    email: '',
    fullName: '',
    aadhar: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  
  const toggleAuthMode = () => {
    setIsLogin(!isLogin);
    setError(null);
    setSuccess(null);
  };

  const handleUserTypeChange = (type) => setUserType(type);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'aadhar' && value.length > 12) return;
    if (name === 'phone' && value.length > 10) return;
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateIndianPhone = (phone) => {
    return /^[6-9]\d{9}$/.test(phone);
  };

  const handleSubmit = async (e) => {
  e.preventDefault();
  setIsLoading(true);
  setError(null);
  setSuccess(null);
  
  try {
    if (!isLogin) {
      if (formData.password !== formData.confirmPassword) {
        throw new Error("Passwords don't match!");
      }
      if (formData.aadhar.length !== 12) {
        throw new Error("Aadhar must be 12 digits!");
      }
      if (!validateIndianPhone(formData.phone)) {
        throw new Error("Please enter a valid Indian phone number!");
      }
    }

    const payload = isLogin 
      ? { email: formData.email, password: formData.password }
      : { 
          fullName: formData.fullName,
          email: formData.email,
          aadhar: formData.aadhar,
          phone: formData.phone,
          userType,
          password: formData.password
        };

    const endpoint = isLogin 
      ? 'http://localhost:2424/user/login'
      : 'http://localhost:2424/user/register';

    const response = await axios.post(endpoint, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const responseUserType = response.data.data.user.userType;
    const accessToken = response.data.data.accessToken;
    const userId = response.data.data.user._id;
    localStorage.setItem('userId', userId);
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('UserType', responseUserType);
    setSuccess(isLogin ? 'Login successful!' : 'Registration successful!');
  
    navigate('/');
    
  } catch (err) {
    setError(err.response?.data?.message || 
           err.message || 
           'Something went wrong. Please try again.');
    console.error('Error:', err);
  } finally {
    setIsLoading(false);
  }
};

  // Rest of your JSX remains exactly the same
  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-purple-900/20 to-blue-900/30 z-10"></div>
        <div className="absolute inset-0 bg-gray-900 overflow-hidden">
          <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1518176258769-f227c798150e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80')] bg-cover bg-center opacity-30"></div>
          <div className="absolute bottom-0 left-0 w-full h-1/3 bg-gradient-to-t from-black/80 to-transparent z-10">
            <div className="absolute bottom-0 left-0 w-full h-1 bg-yellow-400/80"></div>
            <div className="absolute bottom-10 left-0 w-full h-1 bg-gray-700/50"></div>
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ x: i % 2 === 0 ? -100 : 0 }}
                animate={{ x: '100vw' }}
                transition={{
                  duration: 10 + Math.random() * 5,
                  repeat: Infinity,
                  ease: "linear"
                }}
                className="absolute bottom-5 h-1 w-20 bg-yellow-400"
                style={{ left: `${i * 10}%` }}
              ></motion.div>
            ))}
          </div>
        </div>
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ 
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              rotate: Math.random() * 360,
              scale: 0.5 + Math.random()
            }}
            animate={{
              x: Math.random() * 100 - 50,
              y: Math.random() * 100 - 50,
              rotate: 360,
              transition: {
                duration: 30 + Math.random() * 30,
                repeat: Infinity,
                repeatType: "reverse",
                ease: "linear"
              }
            }}
            className="absolute text-white/10"
            style={{
              fontSize: `${20 + Math.random() * 40}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          >
            <FaCar />
          </motion.div>
        ))}
      </div>

      {/* Main Form Container */}
      <div className="w-full max-w-6xl mx-auto">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-col md:flex-row bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden relative z-10 border border-white/20"
        >
          {/* Image Section */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="hidden md:block md:w-1/2 relative overflow-hidden"
          >
            <motion.img
              key={isLogin ? 'login' : 'register'}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              src={isLogin ? carImages.login : carImages.register}
              alt="Car Rental"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/20 flex items-end p-8">
              <div>
                <motion.h2 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-3xl font-bold text-white mb-2"
                >
                  {isLogin ? 'Welcome Back!' : 'Join RideLo Today'}
                </motion.h2>
                <motion.p 
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="text-white/90"
                >
                  {isLogin ? 'Your perfect ride is waiting' : 'Start your journey with us'}
                </motion.p>
              </div>
            </div>
          </motion.div>

          {/* Form Section */}
          <div className="w-full md:w-1/2 p-6 sm:p-8 lg:p-10">
            {/* Mobile Header */}
            <div className="md:hidden flex items-center justify-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2 }}
                className="flex items-center"
              >
                <FaCar className="text-blue-600 text-3xl mr-2" />
                <span className="text-xl font-bold text-gray-800">RideLo</span>
              </motion.div>
            </div>

            {/* Status Messages */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg text-sm flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {error}
                </motion.div>
              )}
              {success && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="mb-4 p-3 bg-green-100 text-green-700 rounded-lg text-sm flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {success}
                </motion.div>
              )}
            </AnimatePresence>

            <motion.h1 
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-2xl font-bold text-gray-800 mb-1"
            >
              {isLogin ? 'Sign In to Your Account' : 'Create New Account'}
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="text-gray-600 mb-6"
            >
              {isLogin ? 'Enter your credentials to continue' : 'Join our car rental community'}
            </motion.p>

            {/* Divider */}
            <div className="flex items-center mb-6">
              <div className="flex-1 h-px bg-gray-300"></div>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            {/* User Type Selection (Registration only) */}
            {!isLogin && (
              <AnimatePresence>
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="mb-6"
                >
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Register as:</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleUserTypeChange('user')}
                      className={`flex items-center justify-center gap-2 py-3 px-4 rounded-lg border-2 transition-all ${userType === 'user' ? 'border-blue-500 bg-blue-50/50 text-blue-600 shadow-sm' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`}
                    >
                      <FaUser className="text-lg" />
                      <span>Regular User</span>
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleUserTypeChange('renter')}
                      className={`flex items-center justify-center gap-2 py-3 px-4 rounded-lg border-2 transition-all ${userType === 'renter' ? 'border-blue-500 bg-blue-50/50 text-blue-600 shadow-sm' : 'border-gray-300 text-gray-600 hover:bg-gray-50'}`}
                    >
                      <FaUserTie className="text-lg" />
                      <span>Car Renter</span>
                    </motion.button>
                  </div>
                </motion.div>
              </AnimatePresence>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <AnimatePresence mode="wait">
                {isLogin ? (
                  <motion.div
                    key="login"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-4"
                  >
                    <motion.div
                      initial={{ x: -10, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition shadow-sm"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </motion.div>
                    <motion.div
                      initial={{ x: -10, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                        Password
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? "text" : "password"}
                          id="password"
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition shadow-sm pr-12"
                          placeholder="••••••••"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                          {showPassword ? <FaEyeSlash /> : <FaEye />}
                        </button>
                      </div>
                      <div className="text-right mt-1">
                        <a href="#" className="text-sm text-blue-600 hover:underline">
                          Forgot password?
                        </a>
                      </div>
                    </motion.div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="register"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-4"
                  >
                    {[
                      { name: 'fullName', label: 'Full Name', type: 'text', placeholder: 'Enter your name...' },
                      { name: 'email', label: 'Email Address', type: 'email', placeholder: 'Enter your email id...' },
                      { name: 'aadhar', label: 'Aadhar Card Number', type: 'number', placeholder: 'Enter your adhar number...' },
                      { name: 'phone', label: 'Phone Number', type: 'tel', placeholder: 'Enter your phone number...' },
                      { 
                        name: 'password', 
                        label: 'Password', 
                        type: showPassword ? 'text' : 'password', 
                        placeholder: '••••••••',
                        icon: (
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          >
                            {showPassword ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        )
                      },
                      { 
                        name: 'confirmPassword', 
                        label: 'Confirm Password', 
                        type: showConfirmPassword ? 'text' : 'password', 
                        placeholder: '••••••••',
                        icon: (
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          >
                            {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        )
                      },
                    ].map((field, index) => (
                      <motion.div
                        key={field.name}
                        initial={{ x: -10, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.1 * (index + 1) }}
                      >
                        <label htmlFor={field.name} className="block text-sm font-medium text-gray-700 mb-1">
                          {field.label}
                        </label>
                        <div className="relative">
                          <input
                            type={field.type}
                            id={field.name}
                            name={field.name}
                            value={formData[field.name]}
                            onChange={handleChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition shadow-sm"
                            placeholder={field.placeholder}
                            {...(field.name === 'aadhar' && { maxLength: 12 })}
                            {...(field.name === 'phone' && { pattern: "[6-9]{1}[0-9]{9}" })}
                          />
                          {field.icon && field.icon}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="mt-6">
                <motion.button
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-blue-500 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all shadow-md relative overflow-hidden"
                >
                  {isLoading && (
                    <motion.div
                      initial={{ x: '-100%' }}
                      animate={{ x: '100%' }}
                      transition={{ 
                        duration: 1.5,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white/30 to-transparent"
                    />
                  )}
                  <span className="relative z-10 flex items-center justify-center">
                    {isLoading && (
                      <motion.span
                        animate={{ rotate: 360 }}
                        transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                        className="inline-block mr-2"
                      >
                        <FaSpinner />
                      </motion.span>
                    )}
                    {isLogin ? 'Sign In' : 'Create Account'}
                  </span>
                </motion.button>
              </div>
            </form>

            <div className="mt-6 text-center">
              <motion.button
                whileHover={{ 
                  scale: 1.05,
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  transition: { duration: 0.2 }
                }}
                whileTap={{ 
                  scale: 0.95,
                  backgroundColor: 'rgba(59, 130, 246, 0.2)'
                }}
                onClick={toggleAuthMode}
                className="text-blue-600 hover:text-blue-800 font-medium text-sm focus:outline-none flex items-center justify-center mx-auto px-3 py-2 rounded-lg cursor-pointer"
                initial={{ opacity: 0.9 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                {isLogin ? "Don't have an account? " : "Already have an account? "}
                <motion.span 
                  className="font-semibold ml-1 underline"
                  whileHover={{ 
                    color: '#1e40af',
                    scale: 1.05,
                    transition: { duration: 0.2 }
                  }}
                >
                  {isLogin ? "Sign up" : "Sign in"}
                </motion.span>
                <motion.svg 
                  className="w-4 h-4 ml-1" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  whileHover={{
                    x: 3,
                    transition: { duration: 0.2 }
                  }}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </motion.svg>
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Register_Login;