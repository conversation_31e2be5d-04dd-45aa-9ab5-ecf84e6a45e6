import React from 'react';
import Navbar from '../../components/Navbar';
import Map from '../../components/Map';
import OurServices from '../../components/OurServices';
import Contact from '../../components/Contact';
import Footer from '../../components/Footer';
// import RenterDashboard from '../Renter/RenterDashboard';

const Dashboard = () => {
  return (
<div className="flex flex-col min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900 relative overflow-hidden">
  {/* Simplified but stunning background */}
  <div className="absolute inset-0 overflow-hidden pointer-events-none">
    
    {/* Soft floating orbs */}
    <div className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-r from-cyan-400/20 to-blue-500/20 rounded-full blur-xl animate-pulse" style={{animationDuration: '4s'}}></div>
    <div className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-r from-purple-400/25 to-pink-500/25 rounded-full blur-lg animate-pulse" style={{animationDelay: '1s', animationDuration: '5s'}}></div>
    <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-gradient-to-r from-indigo-400/15 to-cyan-500/15 rounded-full blur-2xl animate-pulse" style={{animationDelay: '2s', animationDuration: '6s'}}></div>
    <div className="absolute bottom-20 right-20 w-28 h-28 bg-gradient-to-r from-blue-400/20 to-indigo-500/20 rounded-full blur-xl animate-pulse" style={{animationDelay: '0.5s', animationDuration: '4.5s'}}></div>

    {/* Clean car elements */}
    <div className="absolute top-24 left-1/4 text-4xl animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}}>
      <span className="drop-shadow-lg filter">🚗</span>
    </div>
    <div className="absolute top-1/2 right-1/4 text-5xl animate-bounce" style={{animationDelay: '1s', animationDuration: '4s'}}>
      <span className="drop-shadow-lg filter">🏎️</span>
    </div>
    <div className="absolute bottom-1/3 left-1/2 text-4xl animate-bounce" style={{animationDelay: '2s', animationDuration: '3.5s'}}>
      <span className="drop-shadow-lg filter">🚙</span>
    </div>

    {/* Elegant road lines */}
    <div className="absolute top-1/3 left-0 w-full h-1 bg-gradient-to-r from-transparent via-yellow-400/40 to-transparent animate-pulse" style={{animationDuration: '3s'}}></div>
    <div className="absolute bottom-1/3 left-0 w-full h-1 bg-gradient-to-r from-transparent via-orange-400/35 to-transparent animate-pulse" style={{animationDelay: '1.5s', animationDuration: '4s'}}></div>

    {/* Subtle floating particles */}
    <div className="absolute top-16 right-1/3 w-2 h-2 bg-cyan-400/60 rounded-full animate-ping"></div>
    <div className="absolute top-2/3 left-1/4 w-3 h-3 bg-purple-400/50 rounded-full animate-ping" style={{animationDelay: '1s'}}></div>
    <div className="absolute bottom-24 right-1/2 w-2 h-2 bg-blue-400/60 rounded-full animate-ping" style={{animationDelay: '2s'}}></div>

    {/* Smooth gradient waves */}
    <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-transparent to-cyan-600/10 animate-pulse" style={{animationDuration: '8s'}}></div>
    <div className="absolute inset-0 bg-gradient-to-l from-blue-600/8 via-transparent to-indigo-600/8 animate-pulse" style={{animationDelay: '4s', animationDuration: '10s'}}></div>

    {/* Gentle grid pattern */}
    <div className="absolute inset-0 opacity-5">
      <div className="w-full h-full" 
           style={{
             backgroundImage: `
               linear-gradient(90deg, rgba(99, 102, 241, 0.3) 1px, transparent 1px),
               linear-gradient(180deg, rgba(99, 102, 241, 0.3) 1px, transparent 1px)
             `,
             backgroundSize: '80px 80px'
           }}>
      </div>
    </div>
  </div>
  
  <Navbar />
  <div className="flex-grow pt-16 relative z-10"> 
    {/* Clean content area with subtle backdrop */}
    <div className="absolute inset-0 bg-gradient-to-b from-black/5 via-transparent to-black/10 backdrop-blur-sm"></div>
    <div className="relative z-20">
      <Map />
      <OurServices />
      <Contact />
      <Footer />
      {/* <RenterDashboard /> */}
    </div>
  </div>
</div>
  );
};

export default Dashboard;