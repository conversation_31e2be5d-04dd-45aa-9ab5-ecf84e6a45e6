<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ride Lo</title>
    <style>
      /* Clean loader styles */
      #loader-wrapper {
        position: fixed;
        inset: 0;
        background: #ffffff;
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-family: 'Segoe UI', sans-serif;
        transition: opacity 0.3s ease;
      }

      .car-icon {
        width: 70px;
        animation: bounce 1.5s infinite;
      }

      .loader-text {
        margin-top: 15px;
        font-size: 1.1rem;
        color: #333;
        letter-spacing: 0.5px;
        text-align: center;
      }

      @keyframes bounce {
        0%, 100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-15px);
        }
      }
    </style>
  </head>
  <body>
    <!-- Loader -->
    <div id="loader-wrapper">
      <img
        src="https://cdn-icons-png.flaticon.com/512/743/743007.png"
        alt="Car Loading"
        class="car-icon"
      />
      <div class="loader-text">Warming up your ride...</div>
    </div>

    <!-- React Root -->
    <div id="root"></div>

    <!-- React Script -->
    <script type="module" src="/src/main.jsx"></script>

    <!-- Hide loader when React app loads -->
    <script>
      window.addEventListener('load', () => {
        const loader = document.getElementById('loader-wrapper');
        if (loader) {
          loader.style.opacity = '0';
          setTimeout(() => loader.style.display = 'none', 300);
        }
      });
    </script>
  </body>
</html>
