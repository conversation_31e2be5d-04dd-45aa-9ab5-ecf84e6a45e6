{"name": "my-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "framer-motion": "^12.23.6", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-cookie": "^8.0.1", "react-dom": "^19.1.0", "react-feather": "^2.0.10", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.7.0", "sweetalert2": "^11.22.2", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}