import React from 'react';
import { Link } from 'react-router-dom';

const Safety = () => {
  return (
    <div className="bg-gray-200 min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto text-center mb-16">
        <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
          Your <span className="text-blue-600">Safety</span> Comes First
        </h1>
        <p className="mt-5 max-w-xl mx-auto text-xl text-gray-500">
          At RideLo, we prioritize your safety with strict protocols and well-maintained vehicles.
        </p>
      </div>

      {/* Safety Commitment */}
      <div className="max-w-7xl mx-auto mb-16 bg-white shadow-xl rounded-lg p-8 md:p-12">
        <h2 className="text-3xl font-bold text-gray-800 mb-6">Our Safety Commitment</h2>
        <p className="text-lg text-gray-600 mb-6">
          Whether you're renting a <span className="font-semibold">cycle, bike, or car</span>, RideLo ensures every vehicle meets the highest safety standards. We conduct regular inspections, provide sanitization, and offer 24/7 roadside assistance.
        </p>
        
        <div className="grid md:grid-cols-3 gap-6 mt-8">
          <div className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
            <h3 className="text-xl font-semibold text-blue-700 mb-3">Vehicle Inspections</h3>
            <p className="text-gray-600">Every vehicle undergoes a 50-point check before rental, including brakes, tires, and engine health.</p>
          </div>
          <div className="bg-green-50 p-6 rounded-lg border-l-4 border-green-500">
            <h3 className="text-xl font-semibold text-green-700 mb-3">Sanitization</h3>
            <p className="text-gray-600">Deep cleaning after every use, focusing on high-touch areas like steering wheels and handles.</p>
          </div>
          <div className="bg-purple-50 p-6 rounded-lg border-l-4 border-purple-500">
            <h3 className="text-xl font-semibold text-purple-700 mb-3">24/7 Support</h3>
            <p className="text-gray-600">Emergency helpline available for breakdowns, accidents, or medical assistance.</p>
          </div>
        </div>
      </div>

      {/* Safety Guidelines by Vehicle Type */}
      <div className="max-w-7xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Safety Guidelines</h2>
        <div className="grid md:grid-cols-3 gap-8">
          
          {/* Cycle Safety */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://images.unsplash.com/photo-1507035895480-2b3156c31fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
              alt="Cycle Safety" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Cycles</h3>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Always wear a helmet (provided free).</li>
                <li>✔ Use reflectors & lights at night.</li>
                <li>✔ Follow traffic rules; avoid sidewalks.</li>
                <li>✔ Check brakes and tire pressure before riding.</li>
              </ul>
            </div>
          </div>

          {/* Bike Safety */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://tse4.mm.bing.net/th/id/OIP.4WObCiUsQvjy2Gl3-j-WwQHaER?rs=1&pid=ImgDetMain&o=7&rm=3" 
              alt="Bike Safety" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Bikes</h3>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Wear helmets (mandatory) and protective gear.</li>
                <li>✔ Avoid speeding and reckless driving.</li>
                <li>✔ Never ride under the influence of alcohol.</li>
                <li>✔ Use designated bike lanes where available.</li>
              </ul>
            </div>
          </div>

          {/* Car Safety */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://images.unsplash.com/photo-1541899481282-d53bffe3c35d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
              alt="Car Safety" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Cars</h3>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Always wear seatbelts (all passengers).</li>
                <li>✔ Follow speed limits and traffic signals.</li>
                <li>✔ Avoid distractions (no phone use while driving).</li>
                <li>✔ Check fuel, tire pressure, and warning lights.</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Protocols */}
      <div className="max-w-7xl mx-auto mb-16 bg-red-50 rounded-xl p-8 md:p-12 border-l-4 border-red-500">
        <h2 className="text-3xl font-bold text-gray-800 mb-6">Emergency Protocols</h2>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-xl font-semibold text-red-600 mb-3">In Case of an Accident</h3>
            <ul className="text-gray-600 space-y-2">
              <li>🚨 <strong>Call 112</strong> for medical emergencies.</li>
              <li>📞 Contact <strong>RideLo Support (1800-123-4567)</strong> immediately.</li>
              <li>📸 Take photos of the scene for insurance claims.</li>
              <li>🚗 Do not move the vehicle unless necessary.</li>
            </ul>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-red-600 mb-3">Breakdown Assistance</h3>
            <ul className="text-gray-600 space-y-2">
              <li>🔧 Call <strong>RideLo Roadside Help (1800-765-4321)</strong>.</li>
              <li>🛠️ Our team will assist with towing or minor repairs.</li>
              <li>⛽ If out of fuel, we provide emergency refueling.</li>
              <li>🔋 Electric vehicles? We cover battery swaps.</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Safety Partners & Certifications */}
      <div className="max-w-7xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Trusted by Safety Organizations</h2>
        <div className="flex flex-wrap justify-center gap-8">
          <img src="https://tse2.mm.bing.net/th/id/OIP.Rc8mef-ySVBnW5uXzZQ4sAHaHj?rs=1&pid=ImgDetMain&o=7&rm=3" alt="ISO Certified" className="h-16" />
          <img src="https://tse3.mm.bing.net/th/id/OIP._ZTI0a5IhXMfFQbaPixoKQHaHa?rs=1&pid=ImgDetMain&o=7&rm=3" alt="Road Safety Org" className="h-16" />
          <img src="https://cari.gov.in/image/Ministry_of_Ayush.png" alt="Ministry Approved" className="h-16" />
        </div>
      </div>

      {/* Call to Action */}
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Rent with Confidence</h2>
        <p className="text-gray-600 mb-6">Your safety is our top priority. Book a well-maintained vehicle today.</p>
        <Link 
          to="/" 
          className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors"
        >
          Book a Safe Ride
        </Link>
      </div>
    </div>
  );
};

export default Safety;