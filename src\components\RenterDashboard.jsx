import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCar, FaUser, FaSignOutAlt, FaSignInAlt, FaUserPlus, FaHistory, FaShieldAlt, FaInfoCircle, FaEnvelope, FaBars, FaTimes } from 'react-icons/fa';
import { 
  Car, 
  Plus, 
  DollarSign, 
  Calendar, 
  TrendingUp, 
  Eye,
  Settings,
  BarChart3,
  Menu,
  Bell,
  User,
  X,
  ArrowUpRight,
  MapPin,
  Clock,
  CheckCircle,
  Activity,
  ChevronDown,
  Search,
  HelpCircle,
  FileText,
  Shield,
  CreditCard,
  MessageSquare,
  LogOut,
  Upload,
  Camera,
  Edit,
  Trash2,
  MoreVertical,
  Filter,
  Download
} from 'lucide-react';

const RenterDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [userName, setName] = useState("<PERSON> Smith");

  const handleLogout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('userId');
    localStorage.removeItem('UserType');
    setIsLoggedIn(false);
    setUserType('guest');
    window.location.reload();
    navigate('/');
    setIsMobileMenuOpen(false);
  };
  const sendUserIdToBackend = async () => {
    const userId = localStorage.getItem('userId');
    if (!userId) return;

    try {
        const response = await fetch(`http://localhost:2424/user/current?userId=${userId}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const name = data.data.fullName;
        setName(name);

    } catch (error) {
        console.error('Failed to fetch current user:', error);
        throw error;
    }
};

  // Call this function when needed, e.g. on mount
  React.useEffect(() => {
    sendUserIdToBackend();
  }, []);

  
  const stats = [
    {
      title: 'Total Vehicles',
      value: '3',
      subtitle: '3 available',
      icon: Car,
      color: 'blue',
      trend: '+1 this month',
      trendUp: true,
      chartData: [30, 40, 30, 60, 50, 70, 65]
    },
    {
      title: 'Active Bookings',
      value: '1',
      subtitle: 'Currently rented',
      icon: Calendar,
      color: 'green',
      trend: 'Good activity',
      trendUp: true,
      chartData: [10, 20, 15, 25, 20, 30, 28]
    },
    {
      title: 'This Month',
      value: '₹0',
      subtitle: 'Monthly earnings',
      icon: DollarSign,
      color: 'purple',
      trend: 'Start of month',
      trendUp: false,
      chartData: [0, 0, 0, 0, 0, 0, 0]
    },
    {
      title: 'Total Earnings',
      value: '₹2,800',
      subtitle: 'All time',
      icon: TrendingUp,
      color: 'orange',
      trend: '+15% growth',
      trendUp: true,
      chartData: [200, 400, 300, 500, 600, 800, 900]
    }
  ];

  const recentBookings = [
    {
      id: 1,
      vehicle: 'Honda Activa 6G',
      renter: 'John Doe',
      amount: '₹500',
      status: 'completed',
      date: '2 days ago',
      duration: '3 hours',
      image: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 2,
      vehicle: 'Maruti Swift Dzire',
      renter: 'John Doe',
      amount: '₹2,400',
      status: 'active',
      date: 'Today',
      duration: '2 days',
      image: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 3,
      vehicle: 'Hero Cycle',
      renter: 'Sarah Chen',
      amount: '₹120',
      status: 'completed',
      date: '1 week ago',
      duration: '6 hours',
      image: 'https://images.unsplash.com/photo-1485965120184-e220f721d03e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    }
  ];

  const vehicles = [
    {
      id: 1,
      name: 'Honda Activa 6G',
      rate: '₹50/hour',
      status: 'Available',
      location: 'Central Station',
      bookings: 15,
      totalEarnings: '₹1,200',
      image: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 2,
      name: 'Maruti Swift Dzire',
      rate: '₹150/hour',
      status: 'Available',
      location: 'Airport Road',
      bookings: 8,
      totalEarnings: '₹3,600',
      image: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 3,
      name: 'Hero Cycle',
      rate: '₹20/hour',
      status: 'Available',
      location: 'City Center',
      bookings: 22,
      totalEarnings: '₹880',
      image: 'https://images.unsplash.com/photo-1485965120184-e220f721d03e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    }
  ];

  const rentalHistory = [
    {
      id: 1,
      vehicle: 'Honda Activa 6G',
      renter: 'John Doe',
      amount: '₹500',
      status: 'completed',
      startDate: '2024-01-15',
      endDate: '2024-01-15',
      duration: '3 hours',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 2,
      vehicle: 'Maruti Swift Dzire',
      renter: 'John Doe',
      amount: '₹2,400',
      status: 'active',
      startDate: '2024-01-18',
      endDate: '2024-01-20',
      duration: '2 days',
      rating: null,
      image: 'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 3,
      vehicle: 'Hero Cycle',
      renter: 'Sarah Chen',
      amount: '₹120',
      status: 'completed',
      startDate: '2024-01-10',
      endDate: '2024-01-10',
      duration: '6 hours',
      rating: 4,
      image: 'https://images.unsplash.com/photo-1485965120184-e220f721d03e?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    },
    {
      id: 4,
      vehicle: 'Honda Activa 6G',
      renter: 'Mike Wilson',
      amount: '₹150',
      status: 'completed',
      startDate: '2024-01-08',
      endDate: '2024-01-08',
      duration: '2 hours',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1597852074816-d933c7d2b988?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&h=100&q=80'
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'Available':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatGradient = (color) => {
    const gradients = {
      blue: 'bg-blue-100 text-blue-600',
      green: 'bg-green-100 text-green-600',
      purple: 'bg-purple-100 text-purple-600',
      orange: 'bg-orange-100 text-orange-600'
    };
    return gradients[color] || 'bg-blue-100 text-blue-600';
  };

  const renderMiniChart = (data) => {
    const maxValue = Math.max(...data);
    return (
      <div className="flex items-end h-12 w-full space-x-1">
        {data.map((value, index) => (
          <div 
            key={index}
            className="flex-1 bg-gray-200 rounded-t-sm"
            style={{ height: `${(value / maxValue) * 100}%` }}
          />
        ))}
      </div>
    );
  };

  const renderStars = (rating) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <div
            key={star}
            className={`w-4 h-4 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          >
            ★
          </div>
        ))}
      </div>
    );
  };

  // Add Vehicle Component
  const AddVehicleComponent = () => {
    const [vehicleData, setVehicleData] = useState({
      name: '',
      type: '',
      brand: '',
      model: '',
      year: '',
      hourlyRate: '',
      dailyRate: '',
      location: '',
      description: '',
      features: []
    });

    const vehicleTypes = ['Car', 'Bike', 'Scooter', 'Bicycle', 'Electric Vehicle'];
    const features = ['Air Conditioning', 'GPS', 'Bluetooth', 'USB Charger', 'Helmet Included', 'Insurance', 'Fuel Included'];

    const handleFeatureToggle = (feature) => {
      setVehicleData(prev => ({
        ...prev,
        features: prev.features.includes(feature)
          ? prev.features.filter(f => f !== feature)
          : [...prev.features, feature]
      }));
    };

    return (
      <div className="p-4 md:p-6 lg:p-8 max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Add New Vehicle</h1>
          <p className="text-gray-600 mt-2">Fill in the details to list your vehicle for rent</p>
        </div>

        <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
          <div className="p-6">
            {/* Vehicle Images Upload */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Photos</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-blue-400 transition-colors cursor-pointer">
                  <div className="text-center">
                    <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">Add Photo</p>
                  </div>
                </div>
                {[1, 2, 3].map((i) => (
                  <div key={i} className="aspect-square border border-gray-200 rounded-lg bg-gray-50"></div>
                ))}
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Vehicle Name</label>
                <input
                  type="text"
                  value={vehicleData.name}
                  onChange={(e) => setVehicleData({...vehicleData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Honda Activa 6G"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Vehicle Type</label>
                <select
                  value={vehicleData.type}
                  onChange={(e) => setVehicleData({...vehicleData, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select Type</option>
                  {vehicleTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <input
                  type="text"
                  value={vehicleData.brand}
                  onChange={(e) => setVehicleData({...vehicleData, brand: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Honda"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Model</label>
                <input
                  type="text"
                  value={vehicleData.model}
                  onChange={(e) => setVehicleData({...vehicleData, model: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Activa 6G"
                />
              </div>
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Hourly Rate (₹)</label>
                <input
                  type="number"
                  value={vehicleData.hourlyRate}
                  onChange={(e) => setVehicleData({...vehicleData, hourlyRate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Daily Rate (₹)</label>
                <input
                  type="number"
                  value={vehicleData.dailyRate}
                  onChange={(e) => setVehicleData({...vehicleData, dailyRate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="400"
                />
              </div>
            </div>

            {/* Location */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">Pickup Location</label>
              <input
                type="text"
                value={vehicleData.location}
                onChange={(e) => setVehicleData({...vehicleData, location: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Central Station, Delhi"
              />
            </div>

            {/* Features */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">Features & Amenities</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {features.map(feature => (
                  <label key={feature} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={vehicleData.features.includes(feature)}
                      onChange={() => handleFeatureToggle(feature)}
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Description */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                rows={4}
                value={vehicleData.description}
                onChange={(e) => setVehicleData({...vehicleData, description: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describe your vehicle, any special instructions, etc."
              />
            </div>

            {/* Submit Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
              <button className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:shadow-md transition-all font-medium">
                List Vehicle
              </button>
              <button className="flex-1 border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                Save as Draft
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // My Vehicles Component
  const MyVehiclesComponent = () => {
    return (
      <div className="p-4 md:p-6 lg:p-8 max-w-7xl mx-auto">
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">My Vehicles</h1>
            <p className="text-gray-600 mt-2">Manage your listed vehicles and their availability</p>
          </div>
          <button 
            onClick={() => setActiveTab('add')}
            className="mt-4 sm:mt-0 flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2.5 rounded-lg hover:shadow-md transition-all"
          >
            <Plus className="w-4 h-4" />
            <span className="font-medium">Add Vehicle</span>
          </button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Vehicles</p>
                <p className="text-2xl font-bold text-gray-900">3</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Car className="w-5 h-5 text-blue-600" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Available</p>
                <p className="text-2xl font-bold text-green-600">3</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Bookings</p>
                <p className="text-2xl font-bold text-purple-600">45</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Activity className="w-5 h-5 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Vehicles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {vehicles.map((vehicle) => (
            <div key={vehicle.id} className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden hover:shadow-sm transition-shadow">
              <div className="relative">
                <img 
                  src={vehicle.image} 
                  alt={vehicle.name} 
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(vehicle.status)}`}>
                    {vehicle.status}
                  </span>
                </div>
                <div className="absolute top-4 left-4">
                  <button className="p-2 bg-white/90 backdrop-blur-sm rounded-lg hover:bg-white transition-colors">
                    <MoreVertical className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-2">{vehicle.name}</h3>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <DollarSign className="w-4 h-4 flex-shrink-0" />
                    <span>{vehicle.rate}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span>{vehicle.location}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Activity className="w-4 h-4 flex-shrink-0" />
                    <span>{vehicle.bookings} total bookings</span>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div>
                    <p className="text-sm text-gray-500">Total Earnings</p>
                    <p className="font-bold text-gray-900">{vehicle.totalEarnings}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Rental History Component
  const RentalHistoryComponent = () => {
    const [filterStatus, setFilterStatus] = useState('all');
    
    const filteredBookings = filterStatus === 'all' 
      ? rentalHistory 
      : rentalHistory.filter(booking => booking.status === filterStatus);

    return (
      <div className="p-4 md:p-6 lg:p-8 max-w-7xl mx-auto">
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Rental History</h1>
            <p className="text-gray-600 mt-2">View all your past and current bookings</p>
          </div>
          <div className="mt-4 sm:mt-0 flex items-center space-x-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <button className="flex items-center space-x-2 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{rentalHistory.length}</p>
              <p className="text-sm text-gray-500">Total Bookings</p>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{rentalHistory.filter(b => b.status === 'completed').length}</p>
              <p className="text-sm text-gray-500">Completed</p>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{rentalHistory.filter(b => b.status === 'active').length}</p>
              <p className="text-sm text-gray-500">Active</p>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 p-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">₹3,170</p>
              <p className="text-sm text-gray-500">Total Revenue</p>
            </div>
          </div>
        </div>

        {/* Bookings List */}
        <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-gray-900">Recent Bookings</h3>
          </div>
          
          <div className="divide-y divide-gray-200">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img 
                      src={booking.image} 
                      alt={booking.vehicle} 
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div>
                      <h4 className="font-semibold text-gray-900">{booking.vehicle}</h4>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                        <User className="w-3 h-3" />
                        <span>{booking.renter}</span>
                        <span>•</span>
                        <Calendar className="w-3 h-3" />
                        <span>{booking.startDate} to {booking.endDate}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500 mt-1">
                        <Clock className="w-3 h-3" />
                        <span>Duration: {booking.duration}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-bold text-gray-900">{booking.amount}</p>
                        {booking.rating && (
                          <div className="flex items-center mt-1">
                            {renderStars(booking.rating)}
                          </div>
                        )}
                      </div>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                        {booking.status}
                      </span>
                      <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                        <MoreVertical className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Settings Component
  const SettingsComponent = () => {
    const [settings, setSettings] = useState({
      notifications: {
        emailBookings: true,
        smsBookings: false,
        pushNotifications: true,
        marketingEmails: false
      },
      privacy: {
        showProfile: true,
        showEarnings: false,
        allowReviews: true
      }
    });

    return (
      <div className="p-4 md:p-6 lg:p-8 max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">Manage your account preferences and settings</p>
        </div>

        <div className="space-y-6">
          {/* Account Information */}
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-bold text-gray-900">Account Information</h3>
            </div>
            <div className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                  <input
                    type="text"
                    defaultValue={userName}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  <input
                    type="tel"
                    defaultValue="+91 9876543210"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <input
                    type="text"
                    defaultValue="Delhi, India"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-bold text-gray-900">Notification Preferences</h3>
            </div>
            <div className="p-6 space-y-4">
              {Object.entries(settings.notifications).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">
                      {key === 'emailBookings' && 'Email notifications for bookings'}
                      {key === 'smsBookings' && 'SMS notifications for bookings'}
                      {key === 'pushNotifications' && 'Push notifications'}
                      {key === 'marketingEmails' && 'Marketing emails'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {key === 'emailBookings' && 'Get notified via email when someone books your vehicle'}
                      {key === 'smsBookings' && 'Receive SMS alerts for new bookings'}
                      {key === 'pushNotifications' && 'Receive push notifications on your device'}
                      {key === 'marketingEmails' && 'Receive promotional emails and updates'}
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setSettings({
                        ...settings,
                        notifications: {
                          ...settings.notifications,
                          [key]: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-bold text-gray-900">Privacy Settings</h3>
            </div>
            <div className="p-6 space-y-4">
              {Object.entries(settings.privacy).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">
                      {key === 'showProfile' && 'Show profile to renters'}
                      {key === 'showEarnings' && 'Show earnings publicly'}
                      {key === 'allowReviews' && 'Allow reviews and ratings'}
                    </p>
                    <p className="text-sm text-gray-500">
                      {key === 'showProfile' && 'Let potential renters see your profile information'}
                      {key === 'showEarnings' && 'Display your earnings on your public profile'}
                      {key === 'allowReviews' && 'Let renters leave reviews and ratings for your vehicles'}
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setSettings({
                        ...settings,
                        privacy: {
                          ...settings.privacy,
                          [key]: e.target.checked
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:shadow-md transition-all font-medium">
              Save Changes
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="p-4 md:p-6 lg:p-8 max-w-7xl mx-auto">
            {/* Welcome Header */}
            <div className="mb-8">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Dashboard Overview</h1>
              <p className="text-gray-600 mt-2">Welcome back, <span>{userName || "Jane Smith"}</span>! Here's what's happening with your rentals today.</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
              {stats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <div key={index} className="bg-white rounded-xl shadow-xs border border-gray-200 p-6 hover:shadow-sm transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-500 mb-1">{stat.title}</h3>
                        <div className="flex items-end space-x-2">
                          <span className="text-2xl md:text-3xl font-bold text-gray-900">{stat.value}</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">{stat.subtitle}</p>
                      </div>
                      <div className={`p-3 rounded-lg ${getStatGradient(stat.color)}`}>
                        <Icon className="w-5 h-5" />
                      </div>
                    </div>
                    <div className="mt-4">
                      {renderMiniChart(stat.chartData)}
                    </div>
                    <div className="flex items-center space-x-2 mt-3">
                      <ArrowUpRight className={`w-4 h-4 ${stat.trendUp ? 'text-green-500' : 'text-gray-400'}`} />
                      <span className={`text-xs font-medium ${stat.trendUp ? 'text-green-600' : 'text-gray-500'}`}>
                        {stat.trend}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Bottom Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
              {/* Recent Activity */}
              <div className="lg:col-span-2 bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-200 flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Recent Bookings</h3>
                    <p className="text-sm text-gray-600 mt-1">Latest rental activity</p>
                  </div>
                  <button className="text-sm font-medium text-blue-600 hover:text-blue-700 flex items-center">
                    View all <ArrowUpRight className="w-4 h-4 ml-1" />
                  </button>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {recentBookings.map((booking) => (
                      <div key={booking.id} className="flex items-center p-4 hover:bg-gray-50 rounded-lg transition-colors group">
                        <div className="flex-shrink-0 mr-4">
                          <img 
                            src={booking.image} 
                            alt={booking.vehicle} 
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{booking.vehicle}</p>
                          <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                            <User className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{booking.renter}</span>
                            <span>•</span>
                            <DollarSign className="w-3 h-3 flex-shrink-0" />
                            <span>{booking.amount}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                            <Clock className="w-3 h-3 flex-shrink-0" />
                            <span>{booking.date}</span>
                            <span>•</span>
                            <span>{booking.duration}</span>
                          </div>
                        </div>
                        <div className="ml-4 flex-shrink-0">
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-white rounded-xl shadow-xs border border-gray-200 overflow-hidden">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-bold text-gray-900">My Vehicles</h3>
                  <p className="text-sm text-gray-600 mt-1">Current availability status</p>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {vehicles.map((vehicle) => (
                      <div key={vehicle.id} className="flex items-center p-4 hover:bg-gray-50 rounded-lg transition-colors group">
                        <div className="flex-shrink-0 mr-4">
                          <img 
                            src={vehicle.image} 
                            alt={vehicle.name} 
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">{vehicle.name}</p>
                          <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                            <DollarSign className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{vehicle.rate}</span>
                            <span>•</span>
                            <MapPin className="w-3 h-3 flex-shrink-0" />
                            <span className="truncate">{vehicle.location}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                            <Activity className="w-3 h-3 flex-shrink-0" />
                            <span>{vehicle.bookings} bookings</span>
                          </div>
                        </div>
                        <div className="ml-4 flex-shrink-0">
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(vehicle.status)}`}>
                            {vehicle.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h4>
                    <div className="space-y-2">
                      <button 
                        onClick={() => setActiveTab('add')}
                        className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-blue-50 rounded-lg transition-colors border border-gray-200 hover:border-blue-200"
                      >
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-100 rounded-lg mr-3">
                            <Plus className="w-4 h-4 text-blue-600" />
                          </div>
                          <span className="font-medium">Add New Vehicle</span>
                        </div>
                        <ArrowUpRight className="w-4 h-4 text-gray-400" />
                      </button>
                      <button className="w-full flex items-center justify-between p-3 text-left text-gray-700 hover:bg-purple-50 rounded-lg transition-colors border border-gray-200 hover:border-purple-200">
                        <div className="flex items-center">
                          <div className="p-2 bg-purple-100 rounded-lg mr-3">
                            <FileText className="w-4 h-4 text-purple-600" />
                          </div>
                          <span className="font-medium">Generate Report</span>
                        </div>
                        <ArrowUpRight className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'add':
        return <AddVehicleComponent />;
      case 'vehicles':
        return <MyVehiclesComponent />;
      case 'history':
        return <RentalHistoryComponent />;
      case 'settings':
        return <SettingsComponent />;
      default:
        return <div>Page not found</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-72 bg-white border-r border-gray-200 transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 shadow-sm`}>
        {/* Logo */}
        <div className="flex items-center justify-between h-20 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow">
              <span className="text-white font-bold text-lg">RL</span>
            </div>
            <div>
              <span className="text-gray-900 font-bold text-lg">RideLo</span>
              <div className="text-xs text-gray-500">Pro Dashboard</div>
            </div>
          </div>
          <button 
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-6 px-4">
          <div className="space-y-1">
            <button 
              onClick={() => setActiveTab('dashboard')}
              className={`w-full flex items-center space-x-4 px-4 py-3 rounded-xl ${activeTab === 'dashboard' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <BarChart3 className="w-5 h-5" />
              <span className="font-medium">Dashboard</span>
              {activeTab === 'dashboard' && <div className="ml-auto w-2 h-2 bg-blue-500 rounded-full"></div>}
            </button>
            <button 
              onClick={() => setActiveTab('add')}
              className={`w-full flex items-center space-x-4 px-4 py-3 rounded-xl ${activeTab === 'add' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <Plus className="w-5 h-5" />
              <span className="font-medium">Add Vehicle</span>
            </button>
            <button 
              onClick={() => setActiveTab('vehicles')}
              className={`w-full flex items-center space-x-4 px-4 py-3 rounded-xl ${activeTab === 'vehicles' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <Car className="w-5 h-5" />
              <span className="font-medium">My Vehicles</span>
              <div className="ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full">3</div>
            </button>
            <button 
              onClick={() => setActiveTab('history')}
              className={`w-full flex items-center space-x-4 px-4 py-3 rounded-xl ${activeTab === 'history' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <Calendar className="w-5 h-5" />
              <span className="font-medium">Rental History</span>
            </button>
            <button 
              onClick={() => setActiveTab('settings')}
              className={`w-full flex items-center space-x-4 px-4 py-3 rounded-xl ${activeTab === 'settings' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <Settings className="w-5 h-5" />
              <span className="font-medium">Settings</span>
            </button>
          </div>
        </nav>

        {/* Bottom */}
        <div className="absolute bottom-6 left-4 right-4">
          <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-xs">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <div className="text-gray-900 font-medium">{userName || "Jane Smith"}</div>
                <div className="text-xs text-gray-500">Renter Account</div>
              </div>
              <button 
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <ChevronDown className="w-5 h-5" />
              </button>
            </div>
            
            {/* User Dropdown */}
            {userMenuOpen && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="space-y-1">
                  <a href="#" className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <User className="w-4 h-4 mr-3" />
                    Profile
                  </a>
                  <button 
                    onClick={() => setActiveTab('settings')}
                    className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <Settings className="w-4 h-4 mr-3" />
                    Settings
                  </button>
                  <a href="/" onClick={handleLogout} className="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <LogOut className="w-4 h-4 mr-3" />
                    Sign out
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:ml-0 overflow-hidden">
        {/* Top Header */}
        <div className="bg-white shadow-xs border-b border-gray-200 h-20 flex items-center justify-between px-4 md:px-8 sticky top-0 z-30">
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Menu className="w-6 h-6" />
            </button>
            <div className="relative max-w-md w-full hidden md:block">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm"
                placeholder="Search vehicles, bookings..."
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors relative">
              <Bell className="w-5 h-5" />
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center font-medium shadow-sm">3</span>
            </button>
            <button className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              <HelpCircle className="w-5 h-5" />
            </button>
            <button 
              onClick={() => setActiveTab('add')}
              className="hidden md:flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2.5 rounded-lg hover:shadow-md transition-all"
            >
              <Plus className="w-4 h-4" />
              <span className="font-medium">Add Vehicle</span>
            </button>
            <div className="relative">
              <a href="/" onClick={handleLogout}>
               <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        
                        className="flex items-center text-red-500 text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-red-500 hover:shadow-lg hover:shadow-red-500/20"
                      >
                        <FaSignOutAlt className="mr-2 h-4 w-4" />
                        Logout
                      </motion.button>
              </a>
            </div>
          </div>
        </div>

        {/* Dashboard Content */}
        {renderContent()}
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 lg:hidden transition-opacity"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default RenterDashboard;