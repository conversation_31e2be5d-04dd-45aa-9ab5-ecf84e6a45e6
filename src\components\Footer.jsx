import { Phone, Mail, Settings, HelpCircle } from 'react-feather';
import { Link } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-gray-900 to-gray-800 text-white py-12 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-10">
          {/* Services Column */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">
              Services
            </h4>
            <ul className="space-y-4">
              {['City Rides', 'Out of station', 'Premium'].map((item, index) => (
                <li key={index}>
                  <a 
                    href="#" 
                    className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                  >
                    <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Column */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">
              Company
            </h4>
            <ul className="space-y-4">
              <li>
                <Link
                  to="/about"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  to="/safety"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Safety
                </Link>
              </li>
              <li>
                <Link 
                  to="/orders"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Previous Order
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Column */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">
              Team Members
            </h4>
            <ul className="space-y-4">
              <li>
                <Link
                  to="https://www.linkedin.com/in/sahin-nayak-07a614292/"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Sahin Nayak
                </Link>
              </li>
              <li>
                <Link
                  to="https://www.linkedin.com/in/vishal-parui-51a75321b/"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Vishal Parui
                </Link>
              </li>
              <li>
                <Link 
                  to="https://www.linkedin.com/in/04-soumen-mishra/"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Soumen Mishra
                </Link>
              </li>
              <li>
                <Link 
                  to="/orders"
                  className="text-gray-300 hover:text-white transition-colors duration-300 flex items-start"
                >
                  <svg className="w-4 h-4 mt-0.5 mr-2 text-cyan-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                  Sudip Bakuli
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Column */}
          <div>
            <h4 className="text-xl font-bold mb-6 text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">
              Contact
            </h4>
            <ul className="space-y-4">
              <li className="flex items-start text-gray-300 hover:text-white transition-colors duration-300">
                <Phone className="w-5 h-5 mr-3 text-cyan-400 flex-shrink-0" />
                <span>+91 00000 00000</span>
              </li>
              <li className="flex items-start text-gray-300 hover:text-white transition-colors duration-300">
                <Mail className="w-5 h-5 mr-3 text-cyan-400 flex-shrink-0" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-start text-gray-300 hover:text-white transition-colors duration-300">
                <Settings className="w-5 h-5 mr-3 text-cyan-400 flex-shrink-0" />
                <span>Partner with us</span>
              </li>
              <li className="flex items-start text-gray-300 hover:text-white transition-colors duration-300">
                <HelpCircle className="w-5 h-5 mr-3 text-cyan-400 flex-shrink-0" />
                <span>FAQ</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Copyright */}
            <div className="mb-6 md:mb-0">
              <p className="text-gray-400 text-sm md:text-base">
                © {new Date().getFullYear()} RideLo. All rights reserved.
              </p>
            </div>

            {/* Legal Links */}
            <div className="flex flex-wrap justify-center gap-4 md:gap-6">
              {['Terms', 'Privacy', 'Cookies'].map((item, index) => (
                <a 
                  key={index} 
                  href="#" 
                  className="text-gray-400 hover:text-white transition-colors duration-300 text-sm md:text-base"
                >
                  {item}
                </a>
              ))}
            </div>

            {/* Social Icons */}
            {/* <div className="mt-6 md:mt-0 flex space-x-4">
              {['Facebook', 'Twitter', 'Instagram', 'LinkedIn'].map((social, index) => (
                <a 
                  key={index} 
                  href="#" 
                  className="w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-colors duration-300"
                  aria-label={social}
                >
                  <div className="w-5 h-5 text-gray-300 hover:text-white">

                    {social.charAt(0)}
                  </div>
                </a>
              ))}
            </div> */}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;