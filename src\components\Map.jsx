import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { 
  MapPin, Navigation, Clock, Car, Lock, User, AlertCircle, 
  CheckCircle, Loader2, Phone, Mail, Star, ChevronRight, 
  Shield, CreditCard, Calendar, Settings, HelpCircle, Search,
  Bike, CarFront, Check, X
} from 'lucide-react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import 'leaflet-routing-machine';
import 'leaflet-routing-machine/dist/leaflet-routing-machine.css';

// Fix for default marker icons
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom purple marker icon
const createCustomIcon = () => {
  return new L.Icon({
    iconUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzODQgNTEyIj48cGF0aCBmaWxsPSIjNzczN2Q5IiBkPSJNMTkyIDBDODYgMCAwIDg2IDAgMTkyYzAgMTMyLjggMTU2LjYgMzA0IDY0IDQxNmgxMjhlOTIuNi0xMTIgNjQtMjgzLjIgNjQtNDE2QzM4NCA4NiAyOTggMCAxOTIgMHoiLz48Y2lyY2xlIGN4PSIxOTIiIGN5PSIxOTIiIHI9Ijk2IiBmaWxsPSIjZmZmIi8+PC9zdmc+',
    iconSize: [32, 42],
    iconAnchor: [16, 42],
    popupAnchor: [0, -45],
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
    shadowSize: [41, 41]
  });
};

const customIcon = createCustomIcon();

const MapWithRouting = ({ userLocation, destinationCoords }) => {
  const map = useMap();
  const routingControlRef = useRef(null);

  useEffect(() => {
    if (!map || !userLocation || !destinationCoords) return;

    if (routingControlRef.current) {
      map.removeControl(routingControlRef.current);
    }

    routingControlRef.current = L.Routing.control({
      waypoints: [
        L.latLng(userLocation.lat, userLocation.lng),
        L.latLng(destinationCoords.lat, destinationCoords.lng)
      ],
      routeWhileDragging: false,
      showAlternatives: false,
      addWaypoints: false,
      draggableWaypoints: false,
      fitSelectedRoutes: true,
      lineOptions: {
        styles: [{color: '#7c3aed', weight: 5, opacity: 0.7}]
      },
      createMarker: (i, waypoint, n) => {
        if (i === 0) {
          return L.marker(waypoint.latLng, {
            icon: customIcon
          }).bindPopup("Your Location");
        }
        return L.marker(waypoint.latLng, {
          icon: new L.Icon({
            iconUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzODQgNTEyIj48cGF0aCBmaWxsPSIjZGMyNjI2IiBkPSJNMTkyIDBDODYgMCAwIDg2IDAgMTkyYzAgMTMyLjggMTU2LjYgMzA0IDY0IDQxNmgxMjhlOTIuNi0xMTIgNjMtMjgzLjIgNjQtNDE2QzM4NCA4NiAyOTggMCAxOTIgMHoiLz48Y2lyY2xlIGN4PSIxOTIiIGN5PSIxOTIiIHI9Ijk2IiBmaWxsPSIjZmZmIi8+PC9zdmc+',
            iconSize: [32, 42],
            iconAnchor: [16, 42],
            popupAnchor: [0, -45],
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
            shadowSize: [41, 41]
          })
        }).bindPopup("Destination");
      },
      router: L.Routing.osrmv1({
        serviceUrl: 'https://router.project-osrm.org/route/v1',
        timeout: 5000
      })
    }).addTo(map);

    map.fitBounds([
      [userLocation.lat, userLocation.lng],
      [destinationCoords.lat, destinationCoords.lng]
    ]);

    return () => {
      if (routingControlRef.current) {
        map.removeControl(routingControlRef.current);
      }
    };
  }, [userLocation, destinationCoords, map]);

  return null;
};

// Helper function to calculate distance between two coordinates in km
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in km
};

const deg2rad = (deg) => {
  return deg * (Math.PI/180);
};

// Function to generate random nearby coordinates within 6km radius
const generateNearbyCoordinates = (centerLat, centerLng, count) => {
  const coordinates = [];
  for (let i = 0; i < count; i++) {
    // Random distance between 0.5km to 6km
    const distance = 0.5 + Math.random() * 5.5;
    // Random angle
    const angle = Math.random() * Math.PI * 2;
    
    // Convert distance to degrees (approx)
    const lat = centerLat + (distance / 111.32) * Math.cos(angle);
    const lng = centerLng + (distance / (111.32 * Math.cos(deg2rad(centerLat)))) * Math.sin(angle);
    
    coordinates.push({
      lat: parseFloat(lat.toFixed(6)),
      lng: parseFloat(lng.toFixed(6)),
      distance: parseFloat(distance.toFixed(2))
    });
  }
  return coordinates;
};

// Mock vehicle data with locations
const createMockVehicles = (userLat, userLng) => {
  // Generate random nearby locations for vehicles
  const carLocations = generateNearbyCoordinates(userLat, userLng, 10);
  const bikeLocations = generateNearbyCoordinates(userLat, userLng, 5);
  const cycleLocations = generateNearbyCoordinates(userLat, userLng, 3);

  return {
    car: {
      '2': [
        { 
          id: 'car1', 
          name: 'Toyota Camry', 
          type: 'Premium', 
          seats: 2, 
          priceMultiplier: 1.2, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=toyota&modelFamily=camry&modelYear=2022&angle=01',
          location: carLocations[0],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        },
        { 
          id: 'car2', 
          name: 'BMW 3 Series', 
          type: 'Premium', 
          seats: 2, 
          priceMultiplier: 1.5, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=bmw&modelFamily=3-series&modelYear=2022&angle=01',
          location: carLocations[1],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        }
      ],
      '4': [
        { 
          id: 'car3', 
          name: 'Honda Accord', 
          type: 'Daily', 
          seats: 4, 
          priceMultiplier: 1.0, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=honda&modelFamily=accord&modelYear=2022&angle=01',
          location: carLocations[2],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        },
        { 
          id: 'car4', 
          name: 'Toyota Corolla', 
          type: 'Daily', 
          seats: 4, 
          priceMultiplier: 0.9, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=toyota&modelFamily=corolla&modelYear=2022&angle=01',
          location: carLocations[3],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        },
        { 
          id: 'car5', 
          name: 'Mercedes E-Class', 
          type: 'Premium', 
          seats: 4, 
          priceMultiplier: 1.8, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=mercedes&modelFamily=e-class&modelYear=2022&angle=01',
          location: carLocations[4],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        }
      ],
      '6': [
        { 
          id: 'car6', 
          name: 'Toyota Fortuner', 
          type: 'Out of station', 
          seats: 6, 
          priceMultiplier: 1.3, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=toyota&modelFamily=fortuner&modelYear=2022&angle=01',
          location: carLocations[5],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        },
        { 
          id: 'car7', 
          name: 'Ford Explorer', 
          type: 'Out of station', 
          seats: 6, 
          priceMultiplier: 1.4, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=ford&modelFamily=explorer&modelYear=2022&angle=01',
          location: carLocations[6],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        }
      ],
      '8': [
        { 
          id: 'car8', 
          name: 'Toyota Innova', 
          type: 'Out of station', 
          seats: 8, 
          priceMultiplier: 1.6, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=toyota&modelFamily=innova&modelYear=2022&angle=01',
          location: carLocations[7],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        },
        { 
          id: 'car9', 
          name: 'Kia Carnival', 
          type: 'Premium', 
          seats: 8, 
          priceMultiplier: 1.7, 
          image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=kia&modelFamily=carnival&modelYear=2022&angle=01',
          location: carLocations[8],
          driver: `Driver ${Math.floor(Math.random() * 1000)}`,
          rating: (Math.random() * 1 + 4).toFixed(1),
          eta: `${Math.floor(Math.random() * 10) + 5} mins`
        }
      ]
    },
    bike: [
      { 
        id: 'bike1', 
        name: 'Royal Enfield Classic 350', 
        type: 'Daily', 
        priceMultiplier: 0.6, 
        image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=royal-enfield&modelFamily=classic-350&modelYear=2022&angle=01',
        location: bikeLocations[0],
        driver: `Driver ${Math.floor(Math.random() * 1000)}`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        eta: `${Math.floor(Math.random() * 8) + 3} mins`
      },
      { 
        id: 'bike2', 
        name: 'Bajaj Pulsar 220', 
        type: 'Daily', 
        priceMultiplier: 0.5, 
        image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=bajaj&modelFamily=pulsar-220&modelYear=2022&angle=01',
        location: bikeLocations[1],
        driver: `Driver ${Math.floor(Math.random() * 1000)}`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        eta: `${Math.floor(Math.random() * 8) + 3} mins`
      },
      { 
        id: 'bike3', 
        name: 'Harley Davidson Street 750', 
        type: 'Premium', 
        priceMultiplier: 1.2, 
        image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=harley-davidson&modelFamily=street-750&modelYear=2022&angle=01',
        location: bikeLocations[2],
        driver: `Driver ${Math.floor(Math.random() * 1000)}`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        eta: `${Math.floor(Math.random() * 8) + 3} mins`
      }
    ],
    cycle: [
      { 
        id: 'cycle1', 
        name: 'Hero Sprint Next', 
        type: 'Daily', 
        priceMultiplier: 0.4, 
        image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=hero&modelFamily=sprint-next&modelYear=2022&angle=01',
        location: cycleLocations[0],
        driver: `Driver ${Math.floor(Math.random() * 1000)}`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        eta: `${Math.floor(Math.random() * 5) + 2} mins`
      },
      { 
        id: 'cycle2', 
        name: 'Montra Downtown', 
        type: 'Premium', 
        priceMultiplier: 0.7, 
        image: 'https://cdn.imagin.studio/getImage?customer=imgplp&make=montra&modelFamily=downtown&modelYear=2022&angle=01',
        location: cycleLocations[1],
        driver: `Driver ${Math.floor(Math.random() * 1000)}`,
        rating: (Math.random() * 1 + 4).toFixed(1),
        eta: `${Math.floor(Math.random() * 5) + 2} mins`
      }
    ]
  };
};

const Map = () => {
  const [userLocation, setUserLocation] = useState(null);
  const [locationName, setLocationName] = useState('');
  const [locationDetails, setLocationDetails] = useState({
    address: '',
    city: '',
    state: '',
    country: '',
    postcode: '',
    neighborhood: '',
    road: ''
  });
  const [fromAddress, setFromAddress] = useState('');
  const [toAddress, setToAddress] = useState('');
  const [fromPincode, setFromPincode] = useState('');
  const [toPincode, setToPincode] = useState('');
  const [serviceType, setServiceType] = useState('Premium');
  const [vehicleType, setVehicleType] = useState('car');
  const [carSeats, setCarSeats] = useState('4');
  const [hours, setHours] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [locationLoading, setLocationLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [weatherData, setWeatherData] = useState(null);
  const [nearbyPlaces, setNearbyPlaces] = useState([]);
  const [estimatedPrice, setEstimatedPrice] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [activeTab, setActiveTab] = useState('book');
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [destinationCoords, setDestinationCoords] = useState(null);
  const [showVehicleSelection, setShowVehicleSelection] = useState(false);
  const [availableVehicles, setAvailableVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [finalPrice, setFinalPrice] = useState(null);
  const [mockVehicles, setMockVehicles] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchDebounce, setSearchDebounce] = useState(null);
  const [manualLocationInput, setManualLocationInput] = useState('');
  const [isManualLocation, setIsManualLocation] = useState(false);
  const mapRef = useRef(null);

  // Real-time clock
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Check authentication status
  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    setIsAuthenticated(!!accessToken);
  }, []);

  // Get user's current location and generate nearby vehicles
  useEffect(() => {
    const getLocation = async () => {
      if (navigator.geolocation) {
        try {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0
            });
          });
          
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setUserLocation(location);
          
          // Generate mock vehicles near user location
          const vehicles = createMockVehicles(location.lat, location.lng);
          setMockVehicles(vehicles);
          
          await reverseGeocode(location.lat, location.lng);
          await fetchWeatherData(location.lat, location.lng);
          await fetchNearbyPlaces(location.lat, location.lng);
        } catch (err) {
          console.error('Geolocation error:', err);
          setError(`Location access denied: ${err.message}`);
          const defaultLocation = { lat: 22.6074, lng: 88.3249 };
          setUserLocation(defaultLocation);
          
          // Generate mock vehicles near default location
          const vehicles = createMockVehicles(defaultLocation.lat, defaultLocation.lng);
          setMockVehicles(vehicles);
          
          reverseGeocode(defaultLocation.lat, defaultLocation.lng);
        } finally {
          setIsLoading(false);
        }
      } else {
        setError("Geolocation is not supported by this browser.");
        const defaultLocation = { lat: 22.6074, lng: 88.3249 };
        setUserLocation(defaultLocation);
        
        // Generate mock vehicles near default location
        const vehicles = createMockVehicles(defaultLocation.lat, defaultLocation.lng);
        setMockVehicles(vehicles);
        
        reverseGeocode(defaultLocation.lat, defaultLocation.lng);
        setIsLoading(false);
      }
    };

    getLocation();
  }, []);

  const reverseGeocode = async (lat, lng) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );
      const data = await response.json();
      
      if (data && data.address) {
        const { address } = data;
        
        // Set detailed location information
        setLocationDetails({
          address: address,
          city: address.city || address.town || address.village || '',
          state: address.state || '',
          country: address.country || '',
          postcode: address.postcode || '',
          neighborhood: address.neighbourhood || address.suburb || '',
          road: address.road || ''
        });
        
        // Construct a detailed display name
        let displayName = '';
        if (address.road) displayName += `${address.road}, `;
        if (address.neighbourhood) displayName += `${address.neighbourhood}, `;
        if (address.suburb) displayName += `${address.suburb}, `;
        if (address.city) displayName += `${address.city}, `;
        if (address.state) displayName += `${address.state}, `;
        if (address.country) displayName += `${address.country}`;
        
        setLocationName(displayName);
        
        // Set a shorter version for the from address input
        const shortAddress = [
          address.road,
          address.neighbourhood || address.suburb,
          address.city || address.town || address.village
        ].filter(Boolean).join(', ');
        
        setFromAddress(shortAddress);
        setFromPincode(address.postcode || 'Unknown');
      }
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      setLocationName('Your current location');
      setFromAddress('Current Location');
      setFromPincode('Unknown');
    }
  };

  const geocodeAddress = async (address) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=jsonv2&q=${encodeURIComponent(address)}&limit=1`
      );
      const data = await response.json();
      
      if (data && data.length > 0) {
        return {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon),
          displayName: data[0].display_name,
          address: data[0].address,
          pincode: data[0].address?.postcode || 'Unknown'
        };
      }
      return null;
    } catch (error) {
      console.error('Geocoding failed:', error);
      return null;
    }
  };

  const fetchWeatherData = async (lat, lng) => {
    try {
      const hours = new Date().getHours();
      const isDaytime = hours > 6 && hours < 18;
      
      const conditions = isDaytime 
        ? ['Sunny', 'Partly Cloudy', 'Mostly Cloudy'] 
        : ['Clear', 'Partly Cloudy', 'Mostly Cloudy'];
      
      const simulatedWeather = {
        temperature: Math.round(Math.random() * 15 + 20),
        condition: conditions[Math.floor(Math.random() * conditions.length)],
        humidity: Math.round(Math.random() * 40 + 40),
        icon: isDaytime ? '☀️' : '🌙'
      };
      setWeatherData(simulatedWeather);
    } catch (error) {
      console.error('Weather fetch failed:', error);
    }
  };

  const fetchLocationSuggestions = async (query) => {
    if (!query || query.trim().length < 2) {
      setSearchSuggestions([]);
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=jsonv2&q=${encodeURIComponent(query)}&addressdetails=1&limit=5&viewbox=${userLocation.lng-0.1},${userLocation.lat-0.1},${userLocation.lng+0.1},${userLocation.lat+0.1}&bounded=1`
      );
      const data = await response.json();
      
      const suggestions = data.map(item => ({
        displayName: item.display_name,
        lat: parseFloat(item.lat),
        lng: parseFloat(item.lon),
        address: item.address,
        pincode: item.address?.postcode || 'Unknown',
        type: item.type,
        importance: item.importance
      }))
      .sort((a, b) => b.importance - a.importance); // Sort by importance
      
      setSearchSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to fetch location suggestions:', error);
      setSearchSuggestions([]);
    }
  };

  // Improved search with debounce
  useEffect(() => {
    if (searchDebounce) {
      clearTimeout(searchDebounce);
    }
    
    if (searchQuery.trim().length > 1) {
      setSearchDebounce(
        setTimeout(() => {
          fetchLocationSuggestions(searchQuery);
        }, 300)
      );
    } else {
      setSearchSuggestions([]);
    }
    
    return () => {
      if (searchDebounce) {
        clearTimeout(searchDebounce);
      }
    };
  }, [searchQuery]);

  const fetchNearbyPlaces = async (lat, lng) => {
    try {
      const radius = 1;
      const earthRadius = 6371;
      const latDelta = (radius / earthRadius) * (180 / Math.PI);
      const lngDelta = (radius / earthRadius) * (180 / Math.PI) / Math.cos(lat * Math.PI/180);
      
      const bbox = [
        lng - lngDelta,
        lat - latDelta,
        lng + lngDelta,
        lat + latDelta
      ].join(',');

      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${lat}&lon=${lng}&zoom=16&addressdetails=1`
      );
      
      const data = await response.json();
      
      if (data && data.address) {
        const city = data.address.city || data.address.town || data.address.village || 'this location';
        
        const categories = [
          { type: 'Transport', icon: '🚆', items: ['Station', 'Bus Stand', 'Metro'] },
          { type: 'Landmark', icon: '🏛️', items: ['Museum', 'Monument', 'Park'] },
          { type: 'Dining', icon: '🍽️', items: ['Restaurant', 'Cafe', 'Food Court'] },
          { type: 'Shopping', icon: '🛍️', items: ['Mall', 'Market', 'Department Store'] }
        ];
        
        const places = categories.map(category => {
          const randomItem = category.items[Math.floor(Math.random() * category.items.length)];
          const distance = (Math.random() * 2 + 0.5).toFixed(1);
          return {
            name: `${randomItem} near ${city}`,
            distance: `${distance} km`,
            type: category.type,
            rating: (Math.random() * 1 + 4).toFixed(1),
            icon: category.icon,
            description: `Popular ${category.type.toLowerCase()} in ${city}`
          };
        });
        
        setNearbyPlaces(places);
      }
    } catch (error) {
      console.error('Failed to fetch nearby places:', error);
      setNearbyPlaces([
        { 
          name: 'Transport Hub', 
          distance: '1.2 km', 
          type: 'Transport',
          rating: '4.2',
          icon: '🚆',
          description: 'Major transport connection point'
        },
        { 
          name: 'Local Market', 
          distance: '0.8 km', 
          type: 'Shopping',
          rating: '4.5',
          icon: '🛍️',
          description: 'Popular local shopping destination'
        },
        { 
          name: 'City Park', 
          distance: '1.5 km', 
          type: 'Landmark',
          rating: '4.7',
          icon: '🏞️',
          description: 'Beautiful green space in the city'
        },
        { 
          name: 'Restaurant Row', 
          distance: '1.1 km', 
          type: 'Dining',
          rating: '4.3',
          icon: '🍽️',
          description: 'Variety of dining options'
        }
      ]);
    }
  };

  const calculateEstimatedPrice = (serviceType, vehicleType, carSeats, hours) => {
    // Base rates for service types
    const serviceRates = {
      'Daily': { base: 40, discount: 0.1 },
      'Out of station': { base: 60, discount: 0.15 },
      'Premium': { base: 75, discount: 0.2 }
    };
    
    // Vehicle type multipliers
    const vehicleMultipliers = {
      'bike': 0.6,
      'cycle': 0.4,
      'car': 1.0
    };
    
    // Car seat multipliers
    const seatMultipliers = {
      '2': 0.8,
      '4': 1.0,
      '6': 1.3,
      '8': 1.6
    };
    
    const rate = serviceRates[serviceType] || serviceRates['Premium'];
    const vehicleMultiplier = vehicleMultipliers[vehicleType] || 1.0;
    const seatMultiplier = vehicleType === 'car' ? (seatMultipliers[carSeats] || 1.0) : 1.0;
    
    const totalHours = parseFloat(hours) || 1;
    const basePrice = rate.base * totalHours * vehicleMultiplier * seatMultiplier;
    const discount = basePrice * rate.discount;
    const subtotal = basePrice - discount;
    const tax = subtotal * 0.18;
    
    return {
      base: basePrice,
      discount: discount,
      subtotal: subtotal,
      tax: tax,
      total: subtotal + tax,
      savings: discount
    };
  };

  useEffect(() => {
    if (hours && parseFloat(hours) > 0) {
      const price = calculateEstimatedPrice(serviceType, vehicleType, carSeats, hours);
      setEstimatedPrice(price);
    } else {
      setEstimatedPrice(null);
    }
  }, [serviceType, vehicleType, carSeats, hours]);

  const validateForm = () => {
    const errors = {};
    if (!fromAddress.trim()) errors.from = 'Starting location is required';
    if (!toAddress.trim()) errors.to = 'Destination is required';
    if (!hours || parseFloat(hours) <= 0) errors.hours = 'Valid hours required';
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleLocateMe = async () => {
    setLocationLoading(true);
    try {
      const position = await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000
        });
      });
      
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setUserLocation(location);
      
      // Regenerate mock vehicles near new location
      const vehicles = createMockVehicles(location.lat, location.lng);
      setMockVehicles(vehicles);
      
      await reverseGeocode(location.lat, location.lng);
      
      // Reset destination if we're moving
      if (destinationCoords) {
        const distance = calculateDistance(
          location.lat,
          location.lng,
          destinationCoords.lat,
          destinationCoords.lng
        );
        
        if (distance > 2) { // If new location is more than 2km away from destination
          setToAddress('');
          setToPincode('');
          setDestinationCoords(null);
          setError('Destination cleared due to significant location change');
        }
      }
    } catch (err) {
      setError(`Location access failed: ${err.message}`);
    } finally {
      setLocationLoading(false);
    }
  };

  const getAvailableVehicles = () => {
    if (!mockVehicles || !userLocation) return [];
    
    if (vehicleType === 'car') {
      return mockVehicles.car[carSeats]
        .filter(vehicle => 
          (vehicle.type === serviceType || 
          (serviceType === 'Premium' && vehicle.type === 'Out of station')) &&
          calculateDistance(
            userLocation.lat, 
            userLocation.lng, 
            vehicle.location.lat, 
            vehicle.location.lng
          ) <= 6 // Within 6km radius
        )
        .sort((a, b) => a.location.distance - b.location.distance)
        .slice(0, 10); // Limit to 10 nearest vehicles
    } else {
      return mockVehicles[vehicleType]
        .filter(vehicle => 
          (vehicle.type === serviceType || 
          (serviceType === 'Premium' && vehicle.type === 'Daily')) &&
          calculateDistance(
            userLocation.lat, 
            userLocation.lng, 
            vehicle.location.lat, 
            vehicle.location.lng
          ) <= 6 // Within 6km radius
        )
        .sort((a, b) => a.location.distance - b.location.distance)
        .slice(0, 10); // Limit to 10 nearest vehicles
    }
  };

  const handleConfirmBooking = async () => {
    if (!validateForm()) {
      setError('Please fill all required fields correctly');
      return;
    }

    if (!isAuthenticated) {
      setError('Please login to book a ride');
      return;
    }

    // Show vehicle selection modal with nearby vehicles
    const vehicles = getAvailableVehicles();
    
    if (vehicles.length === 0) {
      setError('No available vehicles found within 6km radius. Please try again later.');
      return;
    }
    
    setAvailableVehicles(vehicles);
    setShowVehicleSelection(true);
  };

  const handleVehicleSelect = (vehicle) => {
    setSelectedVehicle(vehicle);
    // Calculate final price with vehicle multiplier
    const price = calculateEstimatedPrice(serviceType, vehicleType, carSeats, hours);
    const finalPrice = price.total * vehicle.priceMultiplier;
    setFinalPrice(finalPrice);
  };

  const handleFinalBooking = async () => {
    if (!selectedVehicle) {
      setError('Please select a vehicle');
      return;
    }

    try {
      setIsLoading(true);
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const bookingDetails = {
        id: `BK${Date.now().toString().slice(-6)}`,
        from: fromAddress,
        to: toAddress,
        fromPincode,
        toPincode,
        serviceType,
        vehicleType,
        carSeats: vehicleType === 'car' ? carSeats : null,
        hours: parseFloat(hours),
        estimatedPrice: finalPrice || estimatedPrice?.total || 0,
        bookingTime: currentTime.toISOString(),
        status: 'Confirmed',
        vehicle: selectedVehicle.name,
        vehicleImage: selectedVehicle.image,
        driver: selectedVehicle.driver,
        driverRating: selectedVehicle.rating,
        driverPhone: `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        vehicleNumber: `${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))} ${Math.floor(Math.random() * 90) + 10} ${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))} ${Math.floor(Math.random() * 9000) + 1000}`,
        eta: selectedVehicle.eta,
        distance: calculateDistance(
          userLocation.lat,
          userLocation.lng,
          selectedVehicle.location.lat,
          selectedVehicle.location.lng
        ).toFixed(2)
      };

      const existingBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
      existingBookings.push(bookingDetails);
      localStorage.setItem('bookings', JSON.stringify(existingBookings));

      setSuccess(`Booking confirmed! Your ${selectedVehicle.name} (${selectedVehicle.driver}, ${selectedVehicle.rating}★) will arrive in ${selectedVehicle.eta}. Booking ID: ${bookingDetails.id}`);
      
      setToAddress('');
      setToPincode('');
      setHours('');
      setEstimatedPrice(null);
      setDestinationCoords(null);
      setShowVehicleSelection(false);
      setSelectedVehicle(null);
      setFinalPrice(null);
      setSearchQuery('');
      
      setIsLoading(false);
    } catch (error) {
      setError('Booking failed. Please try again.');
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setToAddress(suggestion.displayName);
    setToPincode(suggestion.pincode);
    setDestinationCoords({ lat: suggestion.lat, lng: suggestion.lng });
    setSearchSuggestions([]);
    setShowSuggestions(false);
    setSearchQuery('');
    setIsManualLocation(false);
    
    // Zoom to the selected destination
    if (mapRef.current && userLocation) {
      const bounds = L.latLngBounds(
        [userLocation.lat, userLocation.lng],
        [suggestion.lat, suggestion.lng]
      );
      mapRef.current.fitBounds(bounds, { padding: [50, 50] });
    }
  };

  const handleManualLocationSubmit = async () => {
    if (!manualLocationInput.trim()) {
      setError('Please enter a location');
      return;
    }

    setIsLoading(true);
    try {
      const geocodedLocation = await geocodeAddress(manualLocationInput);
      if (geocodedLocation) {
        setToAddress(geocodedLocation.displayName);
        setToPincode(geocodedLocation.pincode);
        setDestinationCoords({ lat: geocodedLocation.lat, lng: geocodedLocation.lng });
        setIsManualLocation(false);
        setSearchQuery('');
        
        // Zoom to the selected destination
        if (mapRef.current && userLocation) {
          const bounds = L.latLngBounds(
            [userLocation.lat, userLocation.lng],
            [geocodedLocation.lat, geocodedLocation.lng]
          );
          mapRef.current.fitBounds(bounds, { padding: [50, 50] });
        }
      } else {
        setError('Could not find the specified location. Please try a different address.');
      }
    } catch (error) {
      setError('Failed to geocode the location. Please try again.');
      console.error('Geocoding error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !userLocation) {
    return (
      <div className="flex items-center justify-center h-screen bg-gradient-to-br from-indigo-900 to-purple-900">
        <div className="text-center">
          <div className="relative inline-block mb-6">
            <Loader2 className="w-16 h-16 animate-spin text-purple-300" />
            <div className="absolute inset-0 flex items-center justify-center">
              <Car className="w-8 h-8 text-white animate-pulse" />
            </div>
          </div>
          <div className="text-2xl text-white font-light mb-2">Locating You...</div>
          <div className="text-purple-300">We're finding the best vehicles near you</div>
          <div className="mt-4 h-1 w-32 bg-purple-700 rounded-full overflow-hidden mx-auto">
            <div className="h-full bg-purple-400 animate-progress"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Full-screen map background */}
      {userLocation && (
        <div className="absolute inset-0 z-0 opacity-20 animate-zoom-in">
          <MapContainer
            center={userLocation}
            zoom={14}
            style={{ height: '100%', width: '100%' }}
            className="z-0"
            zoomControl={false}
            attributionControl={false}
            doubleClickZoom={false}
            scrollWheelZoom={false}
            dragging={false}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <Marker position={userLocation}>
              <Popup className="font-semibold">
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 text-purple-600 mr-2" />
                  You are here
                </div>
              </Popup>
            </Marker>
          </MapContainer>
        </div>
      )}

      {/* Main content */}
      <div className="relative z-10 min-h-screen backdrop-blur-sm">
        {/* Header */}
        <header className="bg-gradient-to-r from-blue-50 to-purple-50 backdrop-blur-md border-b border-gray-200 shadow-inner">
          <div className="w-full">
            <div className="flex items-center justify-center space-x-8 py-3 px-6">
              {weatherData && (
                <div className="flex-1 flex items-center justify-center bg-white/90 px-6 py-3 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
                  <span className="text-4xl mr-4 text-blue-600">{weatherData.icon}</span>
                  <div className="text-center">
                    <div className="font-bold text-gray-800 text-2xl">{weatherData.temperature}°C</div>
                    <div className="text-sm text-gray-600 font-medium mt-1 capitalize">{weatherData.condition}</div>
                  </div>
                </div>
              )}

              <div className="flex-1 flex items-center justify-center bg-white/90 px-6 py-3 rounded-xl shadow-sm border border-gray-200/60 hover:shadow-md transition-all duration-200 hover:scale-[1.02]">
                <svg className="w-6 h-6 text-purple-600 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-center">
                  <div className="font-bold text-gray-800 text-2xl">
                    {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                  <div className="text-sm text-gray-600 font-medium mt-1">
                    {currentTime.toLocaleDateString([], { weekday: 'long', day: 'numeric', month: 'long' })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>
        
        {/* Main content area */}
        <main className="container mx-auto px-4 py-6">
          {/* Notifications */}
          {success && (
            <div className="mb-6 bg-green-50 border-l-4 border-green-500 text-green-700 p-4 rounded-lg flex items-start animate-fade-in">
              <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="font-medium">{success}</p>
                <p className="text-sm mt-1">Driver contact: {success.driverPhone}</p>
              </div>
              <button 
                onClick={() => setSuccess(null)} 
                className="ml-4 text-green-700 hover:text-green-900"
              >
                &times;
              </button>
            </div>
          )}

          {error && (
            <div className="mb-6 bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-lg flex items-start animate-fade-in">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="font-medium">{error}</p>
              </div>
              <button 
                onClick={() => setError(null)} 
                className="ml-4 text-red-700 hover:text-red-900"
              >
                &times;
              </button>
            </div>
          )}

          {/* Booking form */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20 mb-8">
            <div className="flex border-b border-gray-200">
              <button
                onClick={() => setActiveTab('book')}
                className={`flex-1 py-4 px-6 text-center font-medium transition ${activeTab === 'book' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
              >
                <div className="flex items-center justify-center">
                  <Car className="w-5 h-5 mr-2" />
                  Book Ride
                </div>
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`flex-1 py-4 px-6 text-center font-medium transition ${activeTab === 'history' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
              >
                <div className="flex items-center justify-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  My Rides
                </div>
              </button>
            </div>

            <div className="p-6 md:p-8">
              {activeTab === 'book' ? (
                <>
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Where would you like to go?</h2>
                    <p className="text-gray-600">Enjoy premium transportation services</p>
                  </div>

                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <MapPin className="w-4 h-4 text-purple-600 mr-2" />
                          Pickup Location *
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={fromAddress}
                            readOnly
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl bg-gray-50 cursor-not-allowed"
                            placeholder="Your current location"
                          />
                          <button
                            onClick={handleLocateMe}
                            disabled={locationLoading}
                            className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1.5 text-purple-600 hover:text-purple-800 transition disabled:opacity-50"
                            title="Refresh location"
                          >
                            {locationLoading ? (
                              <Loader2 className="w-5 h-5 animate-spin" />
                            ) : (
                              <Navigation className="w-5 h-5" />
                            )}
                          </button>
                        </div>
                        {fromPincode && (
                          <div className="text-xs text-gray-500 mt-1 ml-2">Pincode: {fromPincode}</div>
                        )}
                        {validationErrors.from && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors.from}</p>
                        )}
                        
                        {/* Detailed location information */}
                        {locationDetails.road && (
                          <div className="mt-2 text-sm text-gray-600">
                            <div className="flex items-start">
                              <span className="w-20 text-gray-500">Address:</span>
                              <span>{locationDetails.road}, {locationDetails.neighborhood}</span>
                            </div>
                            <div className="flex items-start">
                              <span className="w-20 text-gray-500">Area:</span>
                              <span>{locationDetails.city}</span>
                            </div>
                            <div className="flex items-start">
                              <span className="w-20 text-gray-500">Region:</span>
                              <span>{locationDetails.state}, {locationDetails.country}</span>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="group">
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <MapPin className="w-4 h-4 text-red-500 mr-2" />
                          Destination *
                        </label>
                        {!isManualLocation ? (
                          <>
                            <div className="relative">
                              <div className="flex">
                                <input
                                  type="text"
                                  value={searchQuery}
                                  onChange={(e) => {
                                    setSearchQuery(e.target.value);
                                    setShowSuggestions(true);
                                  }}
                                  onFocus={() => setShowSuggestions(true)}
                                  className="flex-1 pl-10 pr-4 py-3 border border-gray-300 rounded-l-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition"
                                  placeholder="Search destination..."
                                />
                                <button
                                  onClick={() => {
                                    if (toAddress) {
                                      setSearchQuery(toAddress);
                                      setShowSuggestions(true);
                                    }
                                  }}
                                  className="px-4 bg-gray-100 border-t border-r border-b border-gray-300 rounded-r-xl hover:bg-gray-200 transition"
                                >
                                  <Search className="w-5 h-5 text-gray-500" />
                                </button>
                              </div>
                              <div className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1.5 text-gray-400">
                                <ChevronRight className="w-5 h-5" />
                              </div>
                              
                              {showSuggestions && searchSuggestions.length > 0 && (
                                <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-xl border border-gray-200 max-h-60 overflow-auto">
                                  <div className="px-4 py-2 border-b border-gray-100 bg-gray-50 text-xs text-gray-500">
                                    Search results for "{searchQuery}"
                                  </div>
                                  {searchSuggestions.map((suggestion, index) => (
                                    <div
                                      key={index}
                                      className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-start"
                                      onClick={() => handleSuggestionClick(suggestion)}
                                    >
                                      <div className={`p-2 rounded-lg mr-3 flex-shrink-0 ${
                                        suggestion.type === 'city' || suggestion.type === 'town' ? 
                                          'bg-blue-100 text-blue-600' :
                                          suggestion.type === 'amenity' ?
                                          'bg-green-100 text-green-600' :
                                          'bg-purple-100 text-purple-600'
                                      }`}>
                                        <MapPin className="w-4 h-4" />
                                      </div>
                                      <div className="flex-1 min-w-0">
                                        <div className="font-medium text-gray-800 truncate">
                                          {suggestion.address.road ? `${suggestion.address.road}, ` : ''}
                                          {suggestion.address.neighbourhood || suggestion.address.suburb || ''}
                                        </div>
                                        <div className="text-xs text-gray-500 truncate">
                                          {suggestion.displayName}
                                        </div>
                                        {suggestion.pincode && (
                                          <div className="text-xs text-gray-400 mt-1">Pincode: {suggestion.pincode}</div>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                  <div 
                                    className="px-4 py-3 hover:bg-gray-50 cursor-pointer flex items-center border-t border-gray-100"
                                    onClick={() => {
                                      setIsManualLocation(true);
                                      setManualLocationInput(searchQuery);
                                      setShowSuggestions(false);
                                    }}
                                  >
                                    <div className="p-2 rounded-lg mr-3 bg-yellow-100 text-yellow-600">
                                      <MapPin className="w-4 h-4" />
                                    </div>
                                    <div>
                                      <div className="font-medium text-gray-800">Use exact location</div>
                                      <div className="text-xs text-gray-500">"{searchQuery}"</div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                            {toAddress && (
                              <div className="mt-2 bg-gray-50 rounded-lg p-3 border border-gray-200">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="font-medium text-gray-800">{toAddress.split(',')[0]}</div>
                                    <div className="text-xs text-gray-500">{toAddress}</div>
                                  </div>
                                  <button
                                    onClick={() => {
                                      setToAddress('');
                                      setToPincode('');
                                      setDestinationCoords(null);
                                      setSearchQuery('');
                                    }}
                                    className="text-gray-400 hover:text-gray-600"
                                  >
                                    <X className="w-4 h-4" />
                                  </button>
                                </div>
                                {toPincode && (
                                  <div className="text-xs text-gray-500 mt-1">Pincode: {toPincode}</div>
                                )}
                              </div>
                            )}
                            {!toAddress && searchQuery && searchSuggestions.length === 0 && (
                              <div className="mt-2 text-sm text-gray-600">
                                <button
                                  onClick={() => {
                                    setIsManualLocation(true);
                                    setManualLocationInput(searchQuery);
                                  }}
                                  className="text-purple-600 hover:text-purple-800 underline"
                                >
                                  Can't find your location? Click here to use "{searchQuery}" as your destination
                                </button>
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="space-y-2">
                            <div className="flex">
                              <input
                                type="text"
                                value={manualLocationInput}
                                onChange={(e) => setManualLocationInput(e.target.value)}
                                className="flex-1 px-4 py-3 border border-gray-300 rounded-l-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition"
                                placeholder="Enter exact address"
                              />
                              <button
                                onClick={handleManualLocationSubmit}
                                disabled={isLoading}
                                className="px-4 bg-purple-600 text-white rounded-r-xl hover:bg-purple-700 transition disabled:opacity-50"
                              >
                                {isLoading ? (
                                  <Loader2 className="w-5 h-5 animate-spin" />
                                ) : (
                                  'Set'
                                )}
                              </button>
                            </div>
                            <button
                              onClick={() => {
                                setIsManualLocation(false);
                                setManualLocationInput('');
                              }}
                              className="text-sm text-purple-600 hover:text-purple-800"
                            >
                              ← Back to search suggestions
                            </button>
                          </div>
                        )}
                        {validationErrors.to && (
                          <p className="mt-1 text-sm text-red-600">{validationErrors.to}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <label className="block text-sm font-medium text-gray-700">Service Type</label>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        {[
                          { 
                            type: 'Daily', 
                            price: '40/hour', 
                            description: 'Standard service',
                            icon: <Car className="w-6 h-6" />
                          },
                          { 
                            type: 'Out of station', 
                            price: '₹60/hour', 
                            description: 'Long distance',
                            icon: <Car className="w-6 h-6" />
                          },
                          { 
                            type: 'Premium', 
                            price: '₹75/hour', 
                            description: 'Luxury service',
                            icon: <Car className="w-6 h-6" />
                          }
                        ].map((service) => (
                          <button
                            key={service.type}
                            onClick={() => setServiceType(service.type)}
                            className={`p-4 rounded-xl border-2 transition ${serviceType === service.type ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'}`}
                          >
                            <div className="flex items-start">
                              <div className={`p-2 rounded-lg mr-3 ${serviceType === service.type ? 'bg-purple-100 text-purple-600' : 'bg-gray-100 text-gray-600'}`}>
                                {service.icon}
                              </div>
                              <div className="text-left">
                                <div className="font-medium text-gray-800">{service.type}</div>
                                <div className="text-sm text-gray-600">{service.price}</div>
                                <div className="text-xs text-gray-500 mt-1">{service.description}</div>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <label className="block text-sm font-medium text-gray-700">Vehicle Type</label>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        {[
                          { 
                            type: 'bike', 
                            name: 'Bike',
                            description: 'Fast and economical',
                            icon: <Bike className="w-6 h-6" />
                          },
                          { 
                            type: 'cycle', 
                            name: 'Cycle',
                            description: 'Eco-friendly option',
                            icon: <Bike className="w-6 h-6" />
                          },
                          { 
                            type: 'car', 
                            name: 'Car',
                            description: 'Comfortable ride',
                            icon: <CarFront className="w-6 h-6" />
                          }
                        ].map((vehicle) => (
                          <button
                            key={vehicle.type}
                            onClick={() => {
                              setVehicleType(vehicle.type);
                              if (vehicle.type !== 'car') setCarSeats('4');
                            }}
                            className={`p-4 rounded-xl border-2 transition ${vehicleType === vehicle.type ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'}`}
                          >
                            <div className="flex items-start">
                              <div className={`p-2 rounded-lg mr-3 ${vehicleType === vehicle.type ? 'bg-purple-100 text-purple-600' : 'bg-gray-100 text-gray-600'}`}>
                                {vehicle.icon}
                              </div>
                              <div className="text-left">
                                <div className="font-medium text-gray-800">{vehicle.name}</div>
                                <div className="text-xs text-gray-500 mt-1">{vehicle.description}</div>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {vehicleType === 'car' && (
                      <div className="space-y-4">
                        <label className="block text-sm font-medium text-gray-700">Car Seats</label>
                        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                          {['2', '4', '6', '8'].map((seats) => (
                            <button
                              key={seats}
                              onClick={() => setCarSeats(seats)}
                              className={`p-3 rounded-xl border-2 transition ${carSeats === seats ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'}`}
                            >
                              <div className="flex flex-col items-center">
                                <div className="font-medium text-gray-800">{seats} Seats</div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {seats === '2' ? 'Coupe' : 
                                   seats === '4' ? 'Sedan' : 
                                   seats === '6' ? 'SUV' : 'Van'}
                                </div>
                              </div>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="group">
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <Clock className="w-4 h-4 text-purple-600 mr-2" />
                        Duration (hours) *
                      </label>
                      <input
                        type="number"
                        min="1"
                        step="0.5"
                        value={hours}
                        onChange={(e) => setHours(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition"
                        placeholder="Enter hours needed"
                      />
                      {validationErrors.hours && (
                        <p className="mt-1 text-sm text-red-600">{validationErrors.hours}</p>
                      )}
                    </div>

                    {estimatedPrice && (
                      <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                        <h3 className="font-medium text-gray-800 mb-4 flex items-center">
                          <CreditCard className="w-5 h-5 text-purple-600 mr-2" />
                          Fare Estimate
                        </h3>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Base Fare</span>
                            <span className="font-medium">₹{estimatedPrice.base.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between text-green-600">
                            <span>Discount ({serviceType})</span>
                            <span>-₹{estimatedPrice.discount.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Subtotal</span>
                            <span className="font-medium">₹{estimatedPrice.subtotal.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Tax (18%)</span>
                            <span className="font-medium">₹{estimatedPrice.tax.toFixed(2)}</span>
                          </div>
                          <div className="border-t border-gray-200 pt-3 mt-2 flex justify-between">
                            <span className="font-medium text-gray-800">Total Amount</span>
                            <span className="font-bold text-lg text-purple-600">₹{estimatedPrice.total.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <button
                      onClick={handleConfirmBooking}
                      disabled={isLoading || !isAuthenticated || !toAddress}
                      className={`w-full py-4 px-6 rounded-xl font-bold text-lg transition-all ${isAuthenticated && !isLoading && toAddress ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 shadow-lg hover:shadow-xl' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                    >
                      {isLoading ? (
                        <span className="flex items-center justify-center">
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Processing...
                        </span>
                      ) : isAuthenticated ? (
                        <span className="flex items-center justify-center">
                          Confirm Booking
                        </span>
                      ) : (
                        <span className="flex items-center justify-center">
                          <Lock className="w-5 h-5 mr-2" />
                          Login to Book
                        </span>
                      )}
                    </button>
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Calendar className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-medium text-gray-800 mb-2">Your Ride History</h3>
                  <p className="text-gray-600 mb-6">View your past and upcoming rides</p>
                  <button className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition">
                    View History
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Interactive map section */}
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20 mb-8">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-800 flex items-center">
                <MapPin className="w-5 h-5 text-purple-600 mr-2" />
                Your Location
              </h3>
              <p className="text-gray-600 mt-1">{locationName || 'Loading location...'}</p>
              {fromPincode && (
                <p className="text-xs text-gray-500 mt-1">Pincode: {fromPincode}</p>
              )}
            </div>
            <div className="h-80 w-full">
              {userLocation && (
                <MapContainer
                  center={userLocation}
                  zoom={15}
                  style={{ height: '100%', width: '100%' }}
                  whenCreated={(mapInstance) => { mapRef.current = mapInstance }}
                >
                  <TileLayer
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                  />
                  <Marker position={userLocation} icon={customIcon}>
                    <Popup>
                      <div>
                        <div className="font-medium">Your Location</div>
                        <div className="text-sm text-gray-600 mt-1">{locationDetails.road}</div>
                        <div className="text-xs text-gray-500">{locationDetails.city}, {locationDetails.state}</div>
                        {fromPincode && (
                          <div className="text-xs text-gray-500">Pincode: {fromPincode}</div>
                        )}
                      </div>
                    </Popup>
                  </Marker>
                  
                  {destinationCoords && (
                    <Marker 
                      position={[destinationCoords.lat, destinationCoords.lng]}
                      icon={new L.Icon({
                        iconUrl: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzODQgNTEyIj48cGF0aCBmaWxsPSIjZGMyNjI2IiBkPSJNMTkyIDBDODYgMCAwIDg2IDAgMTkyYzAgMTMyLjggMTU2LjYgMzA0IDY0IDQxNmgxMjhlOTIuNi0xMTIgNjMtMjgzLjIgNjQtNDE2QzM4NCA4NiAyOTggMCAxOTIgMHoiLz48Y2lyY2xlIGN4PSIxOTIiIGN5PSIxOTIiIHI9Ijk2IiBmaWxsPSIjZmZmIi8+PC9zdmc+',
                        iconSize: [32, 42],
                        iconAnchor: [16, 42],
                        popupAnchor: [0, -45],
                        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
                        shadowSize: [41, 41]
                      })}
                    >
                      <Popup>
                        <div>
                          <div className="font-medium">Destination</div>
                          <div className="text-sm text-gray-600 mt-1">{toAddress.split(',')[0]}</div>
                          {toPincode && (
                            <div className="text-xs text-gray-500">Pincode: {toPincode}</div>
                          )}
                        </div>
                      </Popup>
                    </Marker>
                  )}
                  
                  {/* Show available vehicles on map when selection modal is open */}
                  {showVehicleSelection && availableVehicles.map((vehicle, index) => (
                    <Marker
                      key={index}
                      position={[vehicle.location.lat, vehicle.location.lng]}
                      icon={new L.Icon({
                        iconUrl: vehicleType === 'car' ? 
                          'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjNzczN2Q5IiBkPSJNNDcuMSAxMTJoMzcuMWM2LjEgMCAxMS4yIDQuNyAxMS45IDEwLjhMOTYuMSAyMjRoMzE5LjhjMi4yIDAgNC4xIDEuNCA0LjcgMy41bDQwIDx44YzEuNCA1LjUtNC41IDkuMy05LjIgNy4xbC0xMTYuNC00NS4xYz0uOC0uMy0xLjQtMS0xLjQtMS44di0zLjJjMCAwLjcuMyAxLjMuOCAxLjhsNDQuOC0zNi40YzEuMy0xIDEuOC0yLjcgMS4yLTQuMmwtMzItODZjLTIuNC02LjUtMTAuMi05LjEtMTUuNy00LjZMMTc2LjkgMTIwaC05My41Yy04LjkgMC0xNi4zIDcuNC0xNi4zIDE2LjdWMjQwYzAgOC45IDcuNCAxNi43IDE2LjMgMTYuN0gyNDBjOC45IDAgMTYuMy03LjQgMTYuMy0xNi43VjE5Mmg5NnY0OGMwIDguOSA3LjQgMTYuNyAxNi4zIDE2LjdINDA0YzguOSAwIDE2LjMtNy40IDE2LjMtMTYuN1YxNjguN2MwLTguOS03LjQtMTYuNy0xNi4zLTE2LjdIMzUyYy04LjkgMC0xNi4zIDcuNC0xNi4zIDE2Ljd2NDhoLTk2di00OGMwLTguOS03LjQtMTYuNy0xNi4zLTE2LjdINDA3LjF6Ii8+PC9zdmc+' :
                          vehicleType === 'bike' ?
                          'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjNzczN2Q5IiBkPSJNMzg0IDI1NmMwIDg4LjQtNzEuNiAxNjAtMTYwIDE2MHMtMTYwLTcxLjYtMTYwLTE2MCA3MS42LTE2MCAxNjAtMTYwIDE2MCA3MS42IDE2MCAxNjB6bS0xNjAtODBjLTQ0LjIgMC04MCAzNS44LTgwIDgwczM1LjggODAgODAgODAgODAtMzUuOCA4MC04MC0zNS44LTgwLTgwLTgwem0wIDQ4YzE3LjcgMCAzMi0xNC4zIDMyLTMyaC0xNmMwLTguOC03LjItMTYtMTYtMTZzLTE2IDcuMi0xNiAxNnY2NGMwIDguOCA3LjIgMTYgMTYgMTZzMTYtNy4yIDE2LTE2di0xNmgxNmMwIDE3LjcgMTQuMyAzMiAzMiAzMnMzMi0xNC4zIDMyLTMydi0xNmMwLTguOC03LjItMTYtMTYtMTZzLTE2IDcuMi0xNiAxNnYxNmMwIDguOC03LjIgMTYtMTYgMTZzLTE2LTcuMi0xNi0xNnYtNjRjMC0xNy43IDE0LjMtMzIgMzItMzJ6Ii8+PC9zdmc+' :
                          'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA1MTIgNTEyIj48cGF0aCBmaWxsPSIjNzczN2Q5IiBkPSJNMzg0IDI1NmMwIDg4LjQtNzEuNiAxNjAtMTYwIDE2MHMtMTYwLTcxLjYtMTYwLTE2MCA3MS42LTE2MCAxNjAtMTYwIDE2MCA3MS42IDE2MCAxNjB6bS0xNjAtODBjLTQ0LjIgMC04MCAzNS44LTgwIDgwczM1LjggODAgODAgODAgODAtMzUuOCA4MC04MC0zNS44LTgwLTgwLTgwem0wIDQ4YzE3LjcgMCAzMi0xNC4zIDMyLTMyaC0xNmMwLTguOC03LjItMTYtMTYtMTZzLTE2IDcuMi0xNiAxNnY2NGMwIDguOCA3LjIgMTYgMTYgMTZzMTYtNy4yIDE2LTE2di0xNmgxNmMwIDE3LjcgMTQuMyAzMiAzMiAzMnMzMi0xNC4zIDMyLTMydi0xNmMwLTguOC03LjItMTYtMTYtMTZzLTE2IDcuMi0xNiAxNnYxNmMwIDguOC03LjIgMTYtMTYgMTZzLTE2LTcuMi0xNi0xNnYtNjRjMC0xNy43IDE0LjMtMzIgMzItMzJ6Ii8+PC9zdmc+',
                        iconSize: [32, 32],
                        iconAnchor: [16, 32],
                        popupAnchor: [0, -32]
                      })}
                    >
                      <Popup>
                        <div className="text-sm">
                          <div className="font-medium">{vehicle.name}</div>
                          <div>Driver: {vehicle.driver}</div>
                          <div>Rating: {vehicle.rating}★</div>
                          <div>ETA: {vehicle.eta}</div>
                          <div>Distance: {vehicle.location.distance} km</div>
                        </div>
                      </Popup>
                    </Marker>
                  ))}
                  
                  <MapWithRouting 
                    userLocation={userLocation} 
                    destinationCoords={destinationCoords} 
                  />
                </MapContainer>
              )}
            </div>
          </div>

          {/* Nearby places section */}
          {nearbyPlaces.length > 0 && (
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                  <Star className="w-5 h-5 text-yellow-500 mr-2" />
                  Nearby Places
                </h3>
                <p className="text-gray-600 mt-1">Popular destinations near you</p>
              </div>
              <div className="divide-y divide-gray-200">
                {nearbyPlaces.map((place, index) => (
                  <div 
                    key={index}
                    className="p-4 hover:bg-gray-50 transition cursor-pointer"
                    onClick={() => setSelectedPlace(place)}
                  >
                    <div className="flex items-start">
                      <div className="text-2xl mr-4">{place.icon}</div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <h4 className="font-medium text-gray-800">{place.name}</h4>
                          <div className="flex items-center text-sm text-yellow-600">
                            <Star className="w-4 h-4 fill-current mr-1" />
                            {place.rating}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">{place.type}</p>
                        <p className="text-xs text-gray-500 mt-1 flex items-center">
                          <Navigation className="w-3 h-3 mr-1" />
                          {place.distance} away
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Vehicle Selection Modal */}
      {showVehicleSelection && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl overflow-hidden animate-scale-in">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-800">
                  Available {vehicleType === 'car' ? `${carSeats}-Seater` : ''} Vehicles (Within 6km)
                </h3>
                <button
                  onClick={() => {
                    setShowVehicleSelection(false);
                    setSelectedVehicle(null);
                  }}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto p-2">
                  {availableVehicles.map((vehicle) => (
                    <div
                      key={vehicle.id}
                      onClick={() => handleVehicleSelect(vehicle)}
                      className={`p-4 rounded-xl border-2 cursor-pointer transition ${selectedVehicle?.id === vehicle.id ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'}`}
                    >
                      <div className="flex items-start">
                        <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden mr-4 flex-shrink-0">
                          <img 
                            src={vehicle.image} 
                            alt={vehicle.name} 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-800">{vehicle.name}</h4>
                          <div className="flex items-center mt-1">
                            <span className="text-sm text-gray-600">
                              {vehicle.type} • {vehicleType === 'car' ? `${vehicle.seats} Seats` : vehicleType}
                            </span>
                          </div>
                          <div className="flex items-center mt-1 text-sm text-gray-600">
                            <User className="w-4 h-4 mr-1" />
                            <span>{vehicle.driver}</span>
                            <Star className="w-4 h-4 ml-2 mr-1 text-yellow-500 fill-yellow-500" />
                            <span>{vehicle.rating}</span>
                          </div>
                          <div className="flex items-center mt-1 text-sm text-gray-600">
                            <Navigation className="w-4 h-4 mr-1 text-purple-500" />
                            <span>{vehicle.location.distance} km away • ETA: {vehicle.eta}</span>
                          </div>
                          {selectedVehicle?.id === vehicle.id && (
                            <div className="flex items-center mt-2 text-green-600">
                              <Check className="w-4 h-4 mr-1" />
                              <span className="text-sm font-medium">Selected</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedVehicle && (
                  <div className="bg-gray-50 rounded-xl p-4 border border-gray-200 mt-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium text-gray-800">{selectedVehicle.name}</h4>
                        <p className="text-sm text-gray-600">
                          {selectedVehicle.type} • {vehicleType === 'car' ? `${selectedVehicle.seats} Seats` : vehicleType}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          Driver: {selectedVehicle.driver} ({selectedVehicle.rating}★) • {selectedVehicle.eta} away
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-purple-600">
                          ₹{finalPrice?.toFixed(2) || estimatedPrice?.total.toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">Final Price</div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t border-gray-200 mt-4">
                  <button
                    onClick={handleFinalBooking}
                    disabled={!selectedVehicle || isLoading}
                    className={`w-full py-3 px-4 rounded-xl font-bold text-lg transition-all ${selectedVehicle && !isLoading ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 shadow-lg hover:shadow-xl' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                  >
                    {isLoading ? (
                      <span className="flex items-center justify-center">
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        Booking...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center">
                        Confirm & Book Now
                      </span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Place Details Modal */}
      {selectedPlace && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fade-in">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md overflow-hidden animate-scale-in">
            <div className="p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 flex items-center">
                    <span className="text-2xl mr-3">{selectedPlace.icon}</span>
                    {selectedPlace.name}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">{selectedPlace.type}</p>
                </div>
                <button
                  onClick={() => setSelectedPlace(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  &times;
                </button>
              </div>
              
              <div className="mt-6 space-y-4">
                <div className="flex items-center">
                  <Star className="w-5 h-5 text-yellow-500 mr-2" />
                  <span className="font-medium">{selectedPlace.rating} Rating</span>
                </div>
                <div className="flex items-center">
                  <Navigation className="w-5 h-5 text-purple-500 mr-2" />
                  <span>{selectedPlace.distance} from your location</span>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-2">About</h4>
                  <p className="text-gray-600">{selectedPlace.description}</p>
                </div>
                
                <div className="pt-4 border-t border-gray-200">
                  <button
                    onClick={() => {
                      setSearchQuery(selectedPlace.name);
                      setShowSuggestions(true);
                      setSelectedPlace(null);
                    }}
                    className="w-full py-3 px-4 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition font-medium"
                  >
                    Set as Destination
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes scale-in {
          from { 
            opacity: 0;
            transform: scale(0.95);
          }
          to { 
            opacity: 1;
            transform: scale(1);
          }
        }
        
        @keyframes zoom-in {
          from { 
            transform: scale(1.05);
          }
          to { 
            transform: scale(1);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.3s ease-out;
        }
        
        .animate-scale-in {
          animation: scale-in 0.3s ease-out;
        }
        
        .animate-zoom-in {
          animation: zoom-in 10s ease-in-out infinite alternate;
        }
        
        .animate-progress {
          animation: progress 2s ease-in-out infinite;
          width: 100%;
        }
        
        @keyframes progress {
          0% { width: 0%; }
          50% { width: 100%; }
          100% { width: 0%; }
        }
      `}</style>
    </div>
  );
};

export default Map;