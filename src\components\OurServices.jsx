import React from 'react';

const OurServices = () => {
  return (
    <div className="bg-gradient-to-b from-blue-950 to-blue-900">
      {/* Services Section */}
      <div className="px-4 py-16 sm:py-20 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-4">
            Explore <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">Your Ride Options</span>
          </h2>
          <p className="text-lg text-blue-200 max-w-3xl mx-auto">
            Choose from our diverse fleet of eco-friendly to premium vehicles
          </p>
          <div className="flex justify-center mt-6">
            <div className="h-1.5 w-40 bg-gradient-to-r from-cyan-400 to-blue-300 rounded-full"></div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            { 
              name: 'City Cycle', 
              icon: 'https://images.unsplash.com/photo-1485965120184-e220f721d03e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
              description: 'Eco-friendly bicycles for short urban trips',
              color: 'bg-gradient-to-br from-green-500 to-emerald-600',
              price: '₹10/km',
              time: '2-5 mins pickup',
              features: ['Helmet included', 'GPS tracking', '24/7 Availability']
            },
            { 
              name: 'Electric Bike', 
              icon: 'https://images.unsplash.com/photo-1558981806-ec527fa84c39?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
              description: 'Zero-emission scooters for quick city travel',
              color: 'bg-gradient-to-br from-blue-500 to-indigo-600',
              price: '₹15/km',
              time: '3-7 mins pickup',
              features: ['Two helmets', 'Phone charger', 'Top speed 60kmph']
            },
            { 
              name: 'Economy Cab', 
              icon: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
              description: 'Affordable AC cabs for everyday commuting',
              color: 'bg-gradient-to-br from-amber-500 to-orange-600',
              price: '₹18/km',
              time: '5-10 mins pickup',
              features: ['4-seater', 'Free cancellation', 'Live tracking']
            },
            { 
              name: 'Premium Cab', 
              icon: 'https://images.unsplash.com/photo-1541899481282-d53bffe3c35d?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80',
              description: 'Luxury vehicles with professional drivers',
              color: 'bg-gradient-to-br from-purple-500 to-violet-600',
              price: '₹25/km',
              time: '7-12 mins pickup',
              features: ['Executive sedans', 'Free water', 'Priority service']
            }
          ].map((service, index) => (
            <div
              key={index}
              className={`group relative overflow-hidden rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 ${service.color} h-full flex flex-col`}
            >
              <div className="absolute inset-0 bg-black/10 group-hover:bg-black/20 transition-all duration-500"></div>
              
              <div className="relative z-10 mb-6 flex justify-center">
                <div className="w-24 h-24 rounded-full bg-white/10 backdrop-blur-sm border-2 border-white/20 flex items-center justify-center overflow-hidden">
                  <img
                    src={service.icon}
                    alt={service.name}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              </div>
              
              <div className="relative z-10 flex-1 flex flex-col">
                <div className="mb-4 text-center">
                  <h3 className="text-2xl font-bold text-white mb-1">{service.name}</h3>
                  <p className="text-white/90 mb-3">{service.description}</p>
                  
                  {/* <div className="flex justify-center gap-4 mb-4">
                    <div className="bg-white/10 px-3 py-1 rounded-full text-sm text-white">
                      {service.price}
                    </div>
                    <div className="bg-white/10 px-3 py-1 rounded-full text-sm text-white">
                      {service.time}
                    </div>
                  </div> */}
                </div>
                
                <div className="mb-6 flex-1">
                  <ul className="space-y-2">
                    {service.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <svg className="w-4 h-4 mt-1 mr-2 text-white flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-white/90 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* <div className="mt-auto">
                  <button className="w-full py-3 bg-white/10 hover:bg-white/20 text-white font-medium rounded-lg transition duration-300 border border-white/20 hover:border-white/40 flex items-center justify-center gap-2">
                    <span>Book Now</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                </div> */}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Safety Section */}
      <div className="px-4 py-16 bg-gradient-to-br from-blue-800 to-blue-900">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="flex-1 space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-white">
                Your Safety is Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-300">Top Priority</span>
              </h2>
              <div className="w-24 h-1.5 bg-gradient-to-r from-cyan-400 to-blue-300 rounded-full"></div>
              <p className="text-lg text-blue-100">
                We implement rigorous safety measures to ensure peace of mind:
              </p>
              <ul className="space-y-3 text-blue-100">
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-cyan-300 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Verified drivers with background checks</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-cyan-300 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Real-time ride tracking and SOS button</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-cyan-300 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Regular vehicle sanitization and maintenance</span>
                </li>
              </ul>
              <a
                href="#"
                className="inline-flex items-center text-cyan-300 font-semibold text-lg hover:text-white transition-colors group"
              >
                Learn more about safety
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
            </div>

            <div className="flex-1">
              <img
                src="https://images.pexels.com/photos/3729464/pexels-photo-3729464.jpeg?cs=srgb&dl=photo-of-red-car-parked-3729464.jpg&fm=jpg"
                alt="Safety features"
                className="w-full rounded-xl shadow-2xl border-4 border-blue-700 object-cover h-96"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Innovation Section */}
      <div className="px-4 py-16 bg-gradient-to-br from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-10">
            {/* Left Phone Image Section */}
            <div className="bg-gray-800 rounded-2xl p-8 flex justify-center flex-1 w-full shadow-xl">
              <div className="text-center">
                {/* <p className="text-center font-semibold text-gray-300 text-lg mb-3">
                  Buy <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-cyan-400">S1 Pro</span>
                </p> */}
                <img
                  src="https://images.unsplash.com/photo-1593764592116-bfb2a97c642a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                  alt="Electric Scooter"
                  className="w-60 sm:w-72 lg:w-80 object-contain mx-auto transform hover:scale-105 transition duration-500"
                  loading="lazy"
                />
              </div>
            </div>

            {/* Right Text Section */}
            <div className="flex-1 w-full">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Pioneering Innovation Since <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-cyan-400">2025</span>
              </h2>

              <div className="mb-8 flex gap-4">
                <div className="w-1.5 bg-gradient-to-b from-green-400 to-cyan-400 rounded-full"></div>
                <div>
                  <h3 className="font-semibold text-xl text-white mb-2">For Renters</h3>
                  <p className="text-gray-300">
                    We constantly push boundaries with industry-first features that redefine urban mobility,
                    from smart navigation to contactless payments.
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="w-1.5 bg-gradient-to-b from-green-400 to-cyan-400 rounded-full"></div>
                <div>
                  <h3 className="font-semibold text-xl text-white mb-2">For Users</h3>
                  <p className="text-gray-300">
                    Our AI-powered platform helps drivers optimize routes, maximize earnings,
                    and access real-time insights through our intuitive driver app.
                  </p>
                </div>
              </div>

              <button className="mt-8 px-6 py-3 bg-gradient-to-r from-green-500 to-cyan-500 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center gap-2">
                Explore Our Technology
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OurServices;