import React from 'react';
import { Link } from 'react-router-dom';

const About = () => {
  return (
    <div className="bg-gray-200 min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto text-center mb-16">
        <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">
          About <span className="text-blue-600">RideLo</span>
        </h1>
        <p className="mt-5 max-w-xl mx-auto text-xl ">
          Your trusted partner for flexible and affordable vehicle rentals.
        </p>
      </div>

      {/* Company Overview */}
      <div className="max-w-7xl mx-auto mb-16">
        <div className="bg-white shadow-xl rounded-lg p-8 md:p-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-6">Who We Are</h2>
          <p className="text-lg text-gray-600 mb-6">
            Founded in 2025, <span className="font-semibold text-blue-600">RideLo</span> is a leading vehicle rental service provider offering cycles, bikes, cars (regular, luxury, and premium) for short-term and long-term rentals. Our mission is to provide convenient, safe, and eco-friendly mobility solutions.
          </p>
          <div className="grid md:grid-cols-3 gap-8 mt-8">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-blue-700 mb-3">Our Vision</h3>
              <p className="text-gray-600">To revolutionize urban mobility by making vehicle rentals seamless and accessible to everyone.</p>
            </div>
            <div className="bg-green-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-green-700 mb-3">Our Mission</h3>
              <p className="text-gray-600">Deliver affordable, reliable, and sustainable transportation options with top-notch customer service.</p>
            </div>
            <div className="bg-purple-50 p-6 rounded-lg">
              <h3 className="text-xl font-semibold text-purple-700 mb-3">Our Values</h3>
              <p className="text-gray-600">Sustainability, Innovation, Customer-Centric Approach, and Safety First.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Vehicle Fleet Section */}
      <div className="max-w-7xl mx-auto mb-16">
        <h2 className="text-3xl font-bold text-gray-800 mb-8 text-center">Our Fleet</h2>
        <div className="grid md:grid-cols-3 gap-8">
          {/* Cycle Rentals */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://images.unsplash.com/photo-1485965120184-e220f721d03e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
              alt="Cycles" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Cycles</h3>
              <p className="text-gray-600 mb-4">Eco-friendly and affordable for short commutes.</p>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Daily/Weekly Rentals</li>
                <li>✔ City & Mountain Bikes</li>
                <li>✔ Helmet Included</li>
              </ul>
            </div>
          </div>

          {/* Bike Rentals */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://images.unsplash.com/photo-1558981806-ec527fa84c39?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
              alt="Bikes" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Bikes</h3>
              <p className="text-gray-600 mb-4">Fuel-efficient and perfect for city rides.</p>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Scooters & Sports Bikes</li>
                <li>✔ Hourly/Daily Plans</li>
                <li>✔ Insurance Coverage</li>
              </ul>
            </div>
          </div>

          {/* Car Rentals */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <img 
              src="https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" 
              alt="Cars" 
              className="w-full h-48 object-cover"
            />
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-2">Cars</h3>
              <p className="text-gray-600 mb-4">From budget to premium, we have it all.</p>
              <ul className="text-gray-600 space-y-2">
                <li>✔ Regular (Economy)</li>
                <li>✔ Luxury (Sedans/SUVs)</li>
                <li>✔ Premium (Luxury Brands)</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Why Choose Us */}
      <div className="max-w-7xl mx-auto mb-16 bg-blue-600 rounded-xl  p-8 md:p-12">
        <h2 className="text-3xl font-bold mb-8">Why Choose RideLo?</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white bg-opacity-10 p-4 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Flexible Rentals</h3>
            <p>Hourly, daily, weekly, and monthly plans.</p>
          </div>
          <div className="bg-white bg-opacity-10 p-4 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
            <p>Dedicated customer care anytime, anywhere.</p>
          </div>
          <div className="bg-white bg-opacity-10 p-4 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Easy Booking</h3>
            <p>Book online or via our app in minutes.</p>
          </div>
          <div className="bg-white bg-opacity-10 p-4 rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Competitive Pricing</h3>
            <p>No hidden charges, best rates guaranteed.</p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Ready to Ride?</h2>
        <p className=" mb-6">Join thousands of satisfied customers today.</p>
        <Link 
          to="/" 
          className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition-colors"
        >
          Book Now
        </Link>
      </div>
    </div>
  );
};

export default About;