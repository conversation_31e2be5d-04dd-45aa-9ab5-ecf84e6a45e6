import { useEffect, useState } from 'react';
import { FaCar, FaUser, FaSignOutAlt, FaSignInAlt, FaUserPlus, FaHistory, FaShieldAlt, FaInfoCircle, FaEnvelope, FaBars, FaTimes } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useCookies } from 'react-cookie';
import { useNavigate } from 'react-router-dom';

const Navbar = () => {
  const [cookies, removeCookie] = useCookies(['accessToken', 'user_type']);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userType, setUserType] = useState('guest');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    // console.log("Access Token:", accessToken);
    if (accessToken!='undefined' && accessToken) {
      setIsLoggedIn(true);
      setUserType(cookies.user_type || 'user');
    } else {
      setIsLoggedIn(false);
      setUserType('guest');
    }
  }, [cookies.accessToken, cookies.user_type]);

  const handleLogout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('userId');
    localStorage.removeItem('UserType');
    setIsLoggedIn(false);
    setUserType('guest');
    navigate('/');
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { name: 'Browse Cars', path: '/user/dashboard', icon: null },
    { name: 'About', path: '/about', icon: FaInfoCircle },
    { name: 'Safety', path: '/safety', icon: FaShieldAlt },
    // { name: 'Contact', path: '/contact', icon: FaEnvelope },
  ];

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      {/* Navbar */}
      <nav className="bg-gradient-to-r from-purple-900 via-purple-800 to-indigo-900 backdrop-blur-lg shadow-2xl border-b border-purple-500/20 fixed w-full z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 lg:h-18">
            {/* Logo */}
            <motion.div 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => handleNavigation('/user/dashboard')}
              className="flex-shrink-0 flex items-center cursor-pointer"
            >
              <div className="relative">
                <FaCar className="h-8 w-8 lg:h-9 lg:w-9 text-purple-300" />
                <div className="absolute inset-0 h-8 w-8 lg:h-9 lg:w-9 bg-purple-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
              </div>
              <span className="ml-3 text-xl lg:text-2xl font-bold bg-gradient-to-r from-purple-200 to-pink-200 bg-clip-text text-transparent">
                RideLo
              </span>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden lg:block">
              <div className="flex items-center space-x-1">
                {/* Navigation Items */}
                {navItems.map((item) => (
                  <motion.button
                    key={item.name}
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate(item.path)}
                    className="flex items-center text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    {item.icon && <item.icon className="mr-2 h-4 w-4" />}
                    {item.name}
                  </motion.button>
                ))}

                {/* Conditional Items */}
                {isLoggedIn && (
                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/orders')}
                    className="flex items-center text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    <FaHistory className="mr-2 h-4 w-4" />
                    Previous Orders
                  </motion.button>
                )}

                {isLoggedIn && userType === 'renter' && (
                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => navigate('/dashboard')}
                    className="text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    Dashboard
                  </motion.button>
                )}

                {/* Auth Buttons */}
                <div className="ml-4 flex items-center space-x-3">
                  {isLoggedIn ? (
                    <>
                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => navigate('/profile')}
                        className="flex items-center text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                      >
                        <FaUser className="mr-2 h-4 w-4" />
                        Profile
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleLogout}
                        className="flex items-center text-purple-100 hover:text-red-300 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-red-500/20 hover:shadow-lg hover:shadow-red-500/20"
                      >
                        <FaSignOutAlt className="mr-2 h-4 w-4" />
                        Logout
                      </motion.button>
                    </>
                  ) : (
                    <>
                      {/* <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => navigate('/login')}
                        className="flex items-center text-purple-100 hover:text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                      >
                        <FaSignInAlt className="mr-2 h-4 w-4" />
                        Login
                      </motion.button> */}

                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => navigate('/login')}
                        className="flex items-center bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-2 rounded-xl text-sm font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-500/30 transform"
                      >
                        <FaUserPlus className="mr-2 h-4 w-4" />
                        Login / Sign Up
                      </motion.button>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={toggleMobileMenu}
                className="text-purple-200 hover:text-white p-2 rounded-lg transition-colors duration-300 hover:bg-purple-700/30"
              >
                {isMobileMenuOpen ? (
                  <FaTimes className="h-6 w-6" />
                ) : (
                  <FaBars className="h-6 w-6" />
                )}
              </motion.button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-40 lg:hidden"
          >
            {/* Backdrop */}
            <div 
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
              onClick={() => setIsMobileMenuOpen(false)}
            ></div>

            {/* Mobile Menu */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="absolute right-0 top-0 h-full w-80 max-w-sm bg-gradient-to-b from-purple-900 via-purple-800 to-indigo-900 shadow-2xl"
            >
              {/* Mobile Menu Header */}
              <div className="flex items-center justify-between p-6 border-b border-purple-500/20">
                <div className="flex items-center">
                  <FaCar className="h-7 w-7 text-purple-300" />
                  <span className="ml-2 text-lg font-bold bg-gradient-to-r from-purple-200 to-pink-200 bg-clip-text text-transparent">
                    RideLo
                  </span>
                </div>
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="text-purple-200 hover:text-white p-2 rounded-lg transition-colors duration-300"
                >
                  <FaTimes className="h-5 w-5" />
                </motion.button>
              </div>

              {/* Mobile Menu Content */}
              <div className="px-6 py-4 space-y-2">
                {navItems.map((item, index) => (
                  <motion.button
                    key={item.name}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleNavigation(item.path)}
                    className="w-full flex items-center text-purple-100 hover:text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    {item.icon && <item.icon className="mr-3 h-4 w-4" />}
                    {item.name}
                  </motion.button>
                ))}

                {isLoggedIn && (
                  <motion.button
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: navItems.length * 0.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleNavigation('/orders')}
                    className="w-full flex items-center text-purple-100 hover:text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    <FaHistory className="mr-3 h-4 w-4" />
                    Previous Orders
                  </motion.button>
                )}

                {isLoggedIn && userType === 'renter' && (
                  <motion.button
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (navItems.length + 1) * 0.1 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleNavigation('/dashboard')}
                    className="w-full text-left text-purple-100 hover:text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                  >
                    Dashboard
                  </motion.button>
                )}

                {/* Mobile Auth Section */}
                <div className="pt-6 border-t border-purple-500/20 space-y-2">
                  {isLoggedIn ? (
                    <>
                      <motion.button
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleNavigation('/profile')}
                        className="w-full flex items-center text-purple-100 hover:text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                      >
                        <FaUser className="mr-3 h-4 w-4" />
                        Profile
                      </motion.button>

                      <motion.button
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleLogout}
                        className="w-full flex items-center text-purple-100 hover:text-red-300 px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-red-500/20 hover:shadow-lg hover:shadow-red-500/20"
                      >
                        <FaSignOutAlt className="mr-3 h-4 w-4" />
                        Logout
                      </motion.button>
                    </>
                  ) : (
                    <>
                      {/* <motion.button
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleNavigation('/login')}
                        className="w-full flex items-center text-purple-100 hover:text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-purple-700/30 hover:shadow-lg hover:shadow-purple-500/20"
                      >
                        <FaSignInAlt className="mr-3 h-4 w-4" />
                        Login
                      </motion.button> */}

                      <motion.button
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleNavigation('/login')}
                        className="w-full flex items-center justify-center bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl text-sm font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-500/30"
                      >
                        <FaUserPlus className="mr-3 h-4 w-4" />
                        Login / Sign Up
                      </motion.button>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Navbar;